/* eslint-disable jsx-a11y/anchor-is-valid */
import axios from "axios"
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { FiX } from "react-icons/fi"
import { useDispatch } from "react-redux"

import { useToast } from "../hooks"
import { closeSubscriptionModal } from "../store/actions/UserAction"

import Button from "@/components/ui/Button"
import FormInput from "@/components/ui/FormInput"
import TextArea from "@/components/ui/TextArea"

const SubscribeModal = ({ isOpen, onClose }) => {
	const [userName, setUserName] = useState("")
	const [email, setEmail] = useState("")
	const [comments, setComments] = useState("")
	const [phoneNumber, setPhoneNumber] = useState("")
	const [agree, setAgree] = useState(false)
	const { t } = useTranslation(["subscription", "common"])
	const dispatch = useDispatch()
	const Toast = useToast()
	const handleSubmit = async (e) => {
		try {
			e.preventDefault()
			const myForm = e.target
			const formData = new FormData(myForm)

			await axios.post("/", formData, {
				headers: { "Content-Type": "multipart/form-data" },
			})

			setUserName("")
			setEmail("")
			setComments("")
			setPhoneNumber("")
			setAgree(false)

			// Close the modal
			dispatch(closeSubscriptionModal())
			Toast("success", "Thanks we will connect with you as soon as possible")
		} catch (error) {}
		//onClose()
	}

	return (
		<div
			className={
				"fixed z-10 bg-black bg-opacity-50 inset-0 transform ease-in-out " +
				(isOpen
					? "transition-opacity opacity-100 duration-500 h-full translate-x-0 z-[999] overflow-y-auto"
					: "transition-all delay-500 opacity-0 translate-x-full")
			}
		>
			<div
				className={
					"p-8 max-w-sm right-0 absolute bg-white h-min min-h-full shadow-xl delay-400 duration-500 ease-in-out transition-all transform " +
					(isOpen ? "translate-x-0" : "translate-x-full")
				}
			>
				{/* <FaTimes color="red" size={30} /> */}
				<div className="flex items-center">
					<h3>{t("subs_inq")}</h3>
					<FiX
						onClick={() => {
							dispatch(closeSubscriptionModal())
						}}
						color="red"
						size={30}
						className="ml-auto cursor-pointer"
					/>
				</div>

				<p className="mt-3">{t("sub_message")}</p>

				<form name="subscribe" onSubmit={handleSubmit}>
					<input type="hidden" name="form-name" value="subscribe" />
					<div className="form-group mt-8">
						<FormInput
							title={t("common:Name")}
							name="name"
							value={userName}
							type={"text"}
							required
							handleChange={(e) => setUserName(e.target.value)}
						/>
					</div>
					<div className="form-group mt-2">
						<FormInput
							title={t("common:Email")}
							name="email"
							value={email}
							type={"email"}
							required
							handleChange={(e) => setEmail(e.target.value)}
						/>
					</div>
					<div className="form-group mt-2">
						<FormInput
							title={t("common:Telephone")}
							name="phonenumber"
							value={phoneNumber}
							type={"text"}
							required
							handleChange={(e) => setPhoneNumber(e.target.value)}
						/>
					</div>
					<div className="form-group mt-2">
						<TextArea
							name="message"
							value={comments}
							rows={5}
							handleChange={(e) => setComments(e.target.value)}
							label={t("message")}
						/>
					</div>
					<div className="form-group mt-4 flex items-start">
						<div className="flex h-6 items-center">
							<input
								id="policy"
								checked={agree}
								onChange={() => setAgree(!agree)}
								required
								aria-describedby="policy-description"
								name="policy"
								type="checkbox"
								className="h-5 w-5 rounded border-gray-300 text-sky-600 focus:ring-sky-600"
							/>
						</div>
						<label className="ml-4 mt-0 text-xs">
							{t("terms")} {/* TODO Link to the policy */}
							<a className="text-sky-600 cursor-pointer" href="#">
								{/* TODO Link to the policy */}
								{t("policy")}
							</a>
						</label>
					</div>
					<Button title={t("SEND")} disabled={!agree} type="submit" width={"100%"} name="submit" />
				</form>
			</div>
		</div>
	)
}

export default SubscribeModal
