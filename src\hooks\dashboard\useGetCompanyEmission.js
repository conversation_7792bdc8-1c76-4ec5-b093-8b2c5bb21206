import { useQuery } from "@tanstack/react-query"

export default function useGetCompanyEmission(app, input) {
	return useQuery({
		queryKey: ["CompanyEmission", input],
		queryFn: async () => {
			const { RegistrationNumber, year } = input
			if (input.RegistrationNumber !== "") return await app.getCompanyEmission(RegistrationNumber, year)
		},

		enabled: input !== undefined,
		refetchOnMount: true,
		retry: 5,
	})
}
