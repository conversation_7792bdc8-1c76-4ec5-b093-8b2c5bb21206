// Do not change flow of the value
export const CATEGORY8_ENERGY_API_DATA = [
	{ type: "electricity", buildingUsage: "education", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "food sales", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "food service", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "health care", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "inpatient", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "outpatient", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "lodging", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "mercantile", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "retail", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{
		type: "electricity",
		buildingUsage: "enclosed and strip malls",
		buildingArea: 1000,
		unit: "sq.m",
		source: "Norway",
	},
	{ type: "electricity", buildingUsage: "office", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "public assembly", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "public order and safety", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "religious worship", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "service", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "warehouse and storage", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "other", buildingArea: 1000, unit: "sq.m", source: "Norway" },
	{ type: "electricity", buildingUsage: "vacant", buildingArea: 1000, unit: "sq.m", source: "Norway" },
]

// Do not change flow of the value
export const CATEGORY8_BUILDING_TYPE_DATA = [
	{ label: "Education", value: "education" },
	{ label: "Food sales", value: "food sales" },
	{ label: "Food service", value: "food service" },
	{ label: "Health care", value: "health care" },
	{ label: "Inpatient", value: "inpatient" },
	{ label: "Outpatient", value: "outpatient" },
	{ label: "Lodging", value: "lodging" },
	{ label: "Mercantile", value: "mercantile" },
	{ label: "Retail", value: "retail" },
	{ label: "Enclosed and strip malls", value: "enclosed and strip malls" },
	{ label: "Office", value: "office" },
	{ label: "Public assembly", value: "public assembly" },
	{ label: "Public order and safety", value: "public order and safety" },
	{ label: "Religious worship", value: "religious worship" },
	{ label: "Service", value: "service" },
	{ label: "Warehouse and storage", value: "warehouse and storage" },
	{ label: "Other", value: "other" },
	{ label: "Vacant", value: "vacant" },
]

export const BUILDINGS_FACTORS = [
	{
		Category: "Barnehage",
		kwh_year: 261.63,
		kwh_quarter: 65.41,
		kwh_month: 21.8,
	},
	{
		Category: "Kontor",
		kwh_year: 199.75,
		kwh_quarter: 49.94,
		kwh_month: 16.65,
	},
	{
		Category: "Skole",
		kwh_year: 196.75,
		kwh_quarter: 49.19,
		kwh_month: 16.4,
	},
	{
		Category: "Universitet",
		kwh_year: 206,
		kwh_quarter: 51.5,
		kwh_month: 17.17,
	},
	{
		Category: "Sykehus",
		kwh_year: 326,
		kwh_quarter: 81.5,
		kwh_month: 27.17,
	},
	{
		Category: "Sykehjem",
		kwh_year: 270.63,
		kwh_quarter: 67.66,
		kwh_month: 22.55,
	},
	{
		Category: "Hotell",
		kwh_year: 290.13,
		kwh_quarter: 72.53,
		kwh_month: 24.18,
	},
	{
		Category: "Idrettsbygg",
		kwh_year: 250.5,
		kwh_quarter: 62.63,
		kwh_month: 20.88,
	},
	{
		Category: "Forretning",
		kwh_year: 326.75,
		kwh_quarter: 81.69,
		kwh_month: 27.23,
	},
	{
		Category: "Kulturbygning",
		kwh_year: 231.63,
		kwh_quarter: 57.91,
		kwh_month: 19.3,
	},
	{
		Category: "Lett industri",
		kwh_year: 276.5,
		kwh_quarter: 69.13,
		kwh_month: 23.04,
	},
]

//7.9kg/sq.mt per year
export const EMBODIED_CARBON = 7.9 / 12
