import { useQuery } from "@tanstack/react-query"

export default function useGetLastUploadedTransactionDate(app, input) {
	return useQuery({
		queryKey: ["lastUploadedTransactionDate", input],
		queryFn: async () => {
			const { RegistrationNumber } = input
			if (input.RegistrationNumber !== "") return await app.getLastUploadedTransactionDate(RegistrationNumber)
		},

		enabled: input !== undefined,
		//refetchOnWindowFocus: false,
		refetchOnMount: true,
		retry: 5,
	})
}
