import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

const source = axios.CancelToken.source()

const getAirportData = async () => {
  try {
    const res = await axios.get(`${import.meta.env.VITE_AIRPORT_API_HOST}`, {
      headers: {
        authorization: import.meta.env.VITE_EMISSION_API_SECRET
      },
      cancelToken: source.token
    })
    return res.data // Ensure you return res.data or another relevant part of the response
  } catch (error) {
    throw new Error('Failed to fetch airport data')
  }
}

const useGetAirportData = () => {
  return useQuery({
    queryKey: ['AirportData'],
    queryFn: getAirportData
  })
}

export default useGetAirportData
