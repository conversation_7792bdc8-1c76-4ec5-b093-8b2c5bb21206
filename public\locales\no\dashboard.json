{"Including_dwonstream": "Inkludert nedstrøms", "Category9_15": "Kategori 9 - 15 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetch_intensity_error": "Beklager at noe gikk galt, du må legge til inntekt manuelt", "lookup_return_revenue": "Oppslag ga inntekter for året", "not_set_yet": "ikke satt enda", "missing data": "Det er hovedsaklig to grunner til at vi ikke har data på disse. Leverandøren har lagt ned virksomheten eller det er en utenlandsleverandør som vi ikke har noen industrikode på. For at dette ikke skal bli borte bruker vi en sjablongfaktor på disse.", "Røde flagg": "<PERSON><PERSON><PERSON> flagg", "p1": "For at det skal bli best mulig rapportering har vi flagget noen transaksjoner med rødt flagg.", "p1.1": "Disse bør du manuelt sjekke da det er viktige føringer.", "travel_heading": "<PERSON><PERSON>", "travel": "Det er viktig å få gode data på reisevirksomhet. Det er mange som fører reiseregninger over lønn i regnskapet. Av personvernhensyn ser vi bort ifra kontoer for lønn og da kommer de dessverre ikke inn automatisk her i klimaregnskapet. Disse må da legges inn manuelt.", "travel2": "Ansattes pendling til og fra jobb er ikke registrert i regnskapet og må derfor legges til manuelt.", "commuting": "<PERSON><PERSON> til ansattes pendling til og fra jobb", "Emission_Per_Scope": "<PERSON><PERSON><PERSON><PERSON>", "Dashboard": "Dashbord", "total CO2 emissions": "totale CO2 utslipp", "Scope 1 emissions": "Scope 1 utslipp", "Scope 2 emissions": "Scope 2 utslipp", "Scope 3 emissions": "Scope 3 oppstrømsutslipp", "Category": "<PERSON><PERSON><PERSON>", "Scope 3 categories": "Scope 3 Kategorier", "Purchased Goods and Services": "Innkjøpte varer og tjenester", "Capital Goods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fuel and Energy Related Activities": "Drivstoff og energirelaterte aktiviteter", "Upstream Transportation and Distribution": "Oppstrøms transport og distribusjon", "Waste Generated in Operations": "Avfall fra produksjon", "Business Travel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Employee Commuting": "Pendling for ansatte", "Upstream leased assets": "Oppstrøms leasing", "Top 5 suppliers": "Topp 5 leverandører", "Supplier Name": "Leverandørnavn", "Emissions": "<PERSON><PERSON><PERSON><PERSON>", "CO2 added": "CO2 tilført", "Total Co2 added": "Totalt Co2 tilført", "Total emissions": "Totale utslipp", "Production related capital goods": "Produksjonsrelaterte kapitalvarer", "Production related intermediate products": "Produksjonsrelaterte råvarer", "Production related final products": "Produksjonsrelaterte ferdigvarer", "Energy overview": "Energioversikt", "Stationary combustion": "Forbrenning i produksjon", "Mobile combustion": "Transportrelatert forbrenning", "Renewable electricity": "Fornybar energi", "Non-Renewable electricity": "Ikke-fornybar energi", "Nuclear energy": "Kjernekraft", "Total energy to output": "Totalt energiforbruk", "Percentage": "Prosentandel", "Kilowatt hours": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Energy source": "Energikilde", "consumption-based": "Forbruksbasert", "location-based": "Lokasjonsbasert", "market-based": "Markedsbasert", "Carbon intensity": "Karbonintensitet", "Total emissions for": "<PERSON>e utslipp for ", "Revenue for reporting year": "<PERSON><PERSON><PERSON><PERSON> for rapporteringsår ", "Energy": "Energi", "Emissions_by_years": "<PERSON><PERSON><PERSON><PERSON> per år", "Monthly_emissions": "Månedlige u<PERSON>"}