import {
	HtmlEditor,
	Image,
	Inject,
	Link,
	QuickToolbar,
	RichTextEditorComponent,
	Toolbar,
	Table,
} from "@syncfusion/ej2-react-richtexteditor"
import { useRef } from "react"
import { RiEdit2Line } from "react-icons/ri"
import { v1 as uuidv1 } from "uuid"

export default function RichTextEditor({ html, show, handleChangeContent, handleOpenEditor, handleSaveProjectReport }) {
	const rteRef = useRef(null)

	let toolbarSettings = {
		items: [
			"Bold",
			"FontName",
			"FontSize",
			"FontColor",
			"Formats",
			"CreateLink",
			"Alignments",
			"NumberFormatList",
			"BulletFormatList",
			"CreateTable",
			"Image",
			"SourceCode",
		],
	}

	const handleImageUploadSuccess = async (args) => {
		if (!args.e.target.response) return

		const s = JSON.parse(args.e.target.response)
		const path1 = `${args.e.target.responseURL}?alt=media&token=${s.downloadTokens}`

		// Access the editor instance correctly
		if (rteRef.current) {
			const selection = document.getSelection()
			const range = selection.getRangeAt(0)
			const img = document.createElement("img")
			img.src = path1
			img.width = 200
			img.height = 150
			range.insertNode(img)
		}

		const filename = document.querySelector(".e-file-name")
		const fileType = document.querySelector(".e-file-type")

		if (filename && fileType) {
			filename.innerHTML = args.file.name.replace(fileType.innerHTML, "")
			filename.title = path1
		}
	}

	return (
		<div className='mt-6 mb-10'>
			<div className='relative'>
				<div
					dangerouslySetInnerHTML={{
						__html: `  ${html}`,
					}}
				/>
				<div className='print:hidden'>
					<RiEdit2Line
						onClick={() => {
							handleOpenEditor("scope1emission")
						}}
						className='w-6 h-6 hover:cursor-pointer'
					/>
				</div>
			</div>
			{show && (
				<>
					<div className='my-2 ml-8 print:hidden'>
						<RichTextEditorComponent
							ref={rteRef}
							enableAutoUrl={true}
							imageUploadSuccess={handleImageUploadSuccess}
							insertImageSettings={{
								saveUrl: `https://firebasestorage.googleapis.com/v0/b/report-images-dev/o/image${uuidv1()}`,
								width: 200,
								height: 150,
							}}
							value={html}
							change={(content) => {
								handleChangeContent(content)
							}}
							tableSettings={{
								styles: [
									{
										text: "Toggle Border",
										cssClass: "e-dashed-borders",
										command: "Table",
										subCommand: "Dashed",
									},
									{
										text: "Alternate Rows",
										cssClass: "e-alternate-rows",
										command: "Table",
										subCommand: "Alternate",
									},
								],
							}}
							toolbarSettings={toolbarSettings}
						>
							<Inject services={[Toolbar, Image, Link, HtmlEditor, QuickToolbar, Table]} />
						</RichTextEditorComponent>
					</div>
					<div className='w-full flex justify-end mt-2'>
						<button
							onClick={() => {
								handleSaveProjectReport()
								handleOpenEditor("scope1emission")
							}}
							className='bg-blue_d text-white font-semibold py-2 px-4 border border-blue hover:border-transparent rounded print:hidden'
						>
							SAVE
						</button>
					</div>
				</>
			)}
		</div>
	)
}
