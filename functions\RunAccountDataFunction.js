// Electricity prices per month
// https://minspotpris.no/historiskepriser/vis-historiske-str%C3%B8mpriser.html
let price = {
	2016: {
		"01": 36.42,
		"02": 22.92,
		"03": 25.24,
		"04": 25.54,
		"05": 26.28,
		"06": 28.11,
		"07": 27.42,
		"08": 25.15,
		"09": 27.3,
		10: 36.97,
		11: 45.11,
		12: 37.23,
	},
	2017: {
		"01": 35.23,
		"02": 35.27,
		"03": 35.13,
		"04": 34.34,
		"05": 32.95,
		"06": 27.9,
		"07": 30.8,
		"08": 31.22,
		"09": 35.18,
		10: 32.43,
		11: 37.62,
		12: 38.25,
	},
	2018: {
		"01": 39.12,
		"02": 46.3,
		"03": 53.15,
		"04": 46.75,
		"05": 39.46,
		"06": 52.86,
		"07": 62.34,
		"08": 61.85,
		"09": 55.94,
		10: 49.51,
		11: 57.77,
		12: 63.5,
	},
	2019: {
		"01": 68.24,
		"02": 56.44,
		"03": 51.07,
		"04": 49.73,
		"05": 48.24,
		"06": 36.65,
		"07": 42.23,
		"08": 43.25,
		"09": 37.08,
		10: 46.42,
		11: 53.45,
		12: 47.7,
	},
	2020: {
		"01": 30.51,
		"02": 15.79,
		"03": 11.26,
		"04": 6.66,
		"05": 9.94,
		"06": 1.95,
		"07": 1.88,
		"08": 4.86,
		"09": 12.75,
		10: 17.94,
		11: 6.24,
		12: 26.77,
	},
	2021: {
		"01": 62.56,
		"02": 68.13,
		"03": 52.66,
		"04": 56.52,
		"05": 60.72,
		"06": 58.64,
		"07": 72.06,
		"08": 93.54,
		"09": 135.46,
		10: 120.14,
		11: 132.91,
		12: 221.41,
	},
	2022: {
		"01": 175.79,
		"02": 150.67,
		"03": 233.8,
		"04": 217.49,
		"05": 206.21,
		"06": 187.74,
		"07": 208.63,
		"08": 430.36,
		"09": 448.36,
		10: 161.81,
		11: 140.72,
		12: 335.85,
	},
	2023: {
		"01": 126.584,
		"02": 114.88,
		"03": 112.632,
		"04": 110.944,
		"05": 78.608,
		"06": 73.264,
		"07": 36.784,
		"08": 20.064,
		"09": 12.12,
		10: 43.08,
		11: 104.768,
		12: 93.584,
	},
	2024: {
		"01": 132.77,
		"02": 108.75,
		"03": 109.58,
		"04": 100.21,
		"05": 85.24,
		"06": 84.87,
		"07": 75.84,
		"08": 84.59,
		// estimates for 2024
		"09": 81.32,
		10: 88.08,
		11: 104.768,
		12: 110.584,
	},
}
// This one is outdated
let price2 = {
	"01": 62.56,
	"02": 68.13,
	"03": 52.66,
	"04": 56.52,
	"05": 60.72,
	"06": 58.64,
	"07": 72.06,
	"08": 93.54,
	"09": 135.46,
	10: 120.14,
	11: 132.91,
	12: 221.41,
}
//# Disregard this object for now
let price2023_with_area = {
	2023: {
		"01": {
			NO1: 126.584,
			NO2: 126.584,
			NO3: 69.608,
			NO4: 46.38,
			NO5: 128.76,
		},
		"02": {
			NO1: 114.88,
			NO2: 114.952,
			NO3: 48.656,
			NO4: 31.56,
			NO5: 110.8,
		},
		"03": {
			NO1: 112.632,
			NO2: 112.64,
			NO3: 64.152,
			NO4: 49.02,
			NO5: 113.04,
		},
		"04": {
			NO1: 110.944,
			NO2: 110.944,
			NO3: 69.608,
			NO4: 40.37,
			NO5: 112.904,
		},
		"05": {
			NO1: 78.608,
			NO2: 80.656,
			NO3: 31.824,
			NO4: 21.94,
			NO5: 79.024,
		},
		"06": {
			NO1: 73.264,
			NO2: 96.448,
			NO3: 23.464,
			NO4: 21.71,
			NO5: 73.232,
		},
		"07": {
			NO1: 36.784,
			NO2: 68.672,
			NO3: 28.568,
			NO4: 22.31,
			NO5: 37.208,
		},
		"08": {
			NO1: 20.064,
			NO2: 72.904,
			NO3: 20.08,
			NO4: 19.95,
			NO5: 19.008,
		},
		"09": {
			NO1: 1.12,
			NO2: 57.592,
			NO3: 9.904,
			NO4: 9.87,
			NO5: 1.112,
		},
		10: {
			NO1: 43.08,
			NO2: 50.5,
			NO3: 16.27,
			NO4: 16.1,
			NO5: 42.69,
		},
		11: {
			NO1: 104.768,
			NO2: 104.768,
			NO3: 65.392,
			NO4: 62.75,
			NO5: 104.784,
		},
		12: {
			NO1: 93.584,
			NO2: 90.944,
			NO3: 79.168,
			NO4: 68.97,
			NO5: 93.592,
		},
	},
}

// https://www.nve.no/energi/energisystem/kraftproduksjon/hvor-kommer-stroemmen-fra/
// https://www.nve.no/energi/virkemidler/opprinnelsesgarantier-og-varedeklarasjon-for-stroemleverandoerer/varedeklarasjon-for-stroemleverandoerer/
// https://app.electricitymaps.com/zone/NO
let co2_mix = {
	2017: {
		NO: { consumption: 0.042, co2: 0.017, fossil: 0.48, renewables: 0.52, residual: 0.396 },
		SE: { co2: 0.029, fossil: 0.465, renewables: 0.053, nuclear: 0.465, residual: 0.33852 },
	},
	2018: {
		NO: { consumption: 0.042, co2: 0.017, fossil: 0.48, renewables: 0.52, residual: 0.396 },
		SE: { co2: 0.029, fossil: 0.465, renewables: 0.053, nuclear: 0.465, residual: 0.33852 },
	},
	2019: {
		NO: { consumption: 0.042, co2: 0.017, fossil: 0.48, renewables: 0.52, residual: 0.396 },
		SE: { co2: 0.029, fossil: 0.465, renewables: 0.053, nuclear: 0.465, residual: 0.33852 },
	},
	2020: {
		NO: {
			consumption: 0.031,
			co2: 0.008,
			fossil: 0.59,
			renewables: 0.11,
			nuclear: 0.3,
			residual: 0.39,
			nuclearWaste: 1.13,
		},
		SE: { co2: 0.027, fossil: 0.547, renewables: 0.08, nuclear: 0.373, residual: 0.36527 },
	},
	2021: {
		NO: {
			consumption: 0.032,
			co2: 0.011,
			fossil: 0.62,
			renewables: 0.15,
			nuclear: 0.23,
			residual: 0.397,
			nuclearWaste: 0.89,
		},
		SE: { co2: 0.029, fossil: 0.55, renewables: 0.1283, nuclear: 0.32, residual: 0.372 },
	},
	2022: {
		NO: {
			consumption: 0.035,
			co2: 0.019,
			fossil: 0.7,
			renewables: 0.14,
			nuclear: 0.16,
			residual: 0.502,
			nuclearWaste: 0.59,
		},
		SE: { co2: 0.029, fossil: 0.55, renewables: 0.1283, nuclear: 0.32, residual: 0.372 },
	},
	2023: {
		NO: {
			consumption: 0.035,
			co2: 0.015,
			fossil: 0.86,
			renewables: 0.03,
			nuclear: 0.11,
			residual: 0.599,
			nuclearWaste: 0.38,
		},
		SE: { co2: 0.029, fossil: 0.55, renewables: 0.1283, nuclear: 0.32, residual: 0.372 },
	},
	2024: {
		NO: {
			consumption: 0.035,
			co2: 0.015,
			fossil: 0.86,
			renewables: 0.03,
			nuclear: 0.11,
			residual: 0.599,
			nuclearWaste: 0.38,
		},
		SE: { co2: 0.029, fossil: 0.55, renewables: 0.1283, nuclear: 0.32, residual: 0.372 },
	},
}

//# Disregard this object for now
let co2_mix2 = {
	NO: {
		2019: { co2: 0.017, fossil: 0.48, residual: 0.396 },
		2020: { co2: 0.008, fossil: 0.41, residual: 0.402 },
		2021: { co2: 0.011, fossil: 0.38, residual: 0.405 },
	},
	SE: {
		2019: { co2: 0.008, fossil: 0.41, residual: 0.402 },
		2020: { co2: 0.008, fossil: 0.41, residual: 0.402 },
		2021: { co2: 0.011, fossil: 0.38, residual: 0.405 },
	},
	EU27: { 2019: 0.3, 2020: 0.231, 2021: 0.231, 2022: 0.5312 },
	nordic: { 2019: 0.396, 2020: 0.402, 2021: 0.405 },
}

// Fuel prices
// https://www.drivkraftnorge.no/Tall-og-fakta/prisstatistikk/
let fuelPrices = {
	2019: {
		12: {
			petrol: 15.7,
			diesel: 14.83,
		},
	},
	2020: {
		"01": {
			petrol: 15.92,
			diesel: 15.4,
		},
		"02": {
			petrol: 15.6,
			diesel: 14.84,
		},
		"03": {
			petrol: 15.19,
			diesel: 14.34,
		},
		"04": {
			petrol: 13.69,
			diesel: 13.65,
		},
		"05": {
			petrol: 14.07,
			diesel: 13.47,
		},
		"06": {
			petrol: 14.78,
			diesel: 13.73,
		},
		"07": {
			petrol: 15.35,
			diesel: 14.41,
		},
		"08": {
			petrol: 14.86,
			diesel: 14.01,
		},
		"09": {
			petrol: 14.38,
			diesel: 13.15,
		},
		10: {
			petrol: 14.43,
			diesel: 13.2,
		},
		11: {
			petrol: 13.56,
			diesel: 12.58,
		},
		12: {
			petrol: 14.44,
			diesel: 13.56,
		},
	},
	2021: {
		"01": {
			petrol: 13.036,
			diesel: 13.2,
		},
		"02": {
			petrol: 13.056,
			diesel: 13.104,
		},
		"03": {
			petrol: 13.158,
			diesel: 13.032,
		},
		"04": {
			petrol: 13.28,
			diesel: 13.176,
		},
		"05": {
			petrol: 13.344,
			diesel: 13.2,
		},
		"06": {
			petrol: 13.6,
			diesel: 13.608,
		},
		"07": {
			petrol: 14.2,
			diesel: 13.648,
		},
		"08": {
			petrol: 14.1,
			diesel: 13.208,
		},
		"09": {
			petrol: 13.64,
			diesel: 13.098,
		},
		10: {
			petrol: 13.896,
			diesel: 13.024,
		},
		11: {
			petrol: 14.12,
			diesel: 13.408,
		},
		12: {
			petrol: 14.064,
			diesel: 13.408,
		},
	},
	2022: {
		"01": {
			petrol: 14.912,
			diesel: 14.032,
		},
		"02": {
			petrol: 15.424,
			diesel: 14.584,
		},
		"03": {
			petrol: 17.632,
			diesel: 17.648,
		},
		"04": {
			petrol: 16.992,
			diesel: 16.792,
		},
		"05": {
			petrol: 18.368,
			diesel: 17.912,
		},
		"06": {
			petrol: 20.928,
			diesel: 19.664,
		},
		"07": {
			petrol: 20.048,
			diesel: 19.712,
		},
		"08": {
			petrol: 17.112,
			diesel: 17.072,
		},
		"09": {
			petrol: 17.12,
			diesel: 17.744,
		},
		10: {
			petrol: 18.312,
			diesel: 18.44,
		},
		11: {
			petrol: 16.832,
			diesel: 17.944,
		},
		12: {
			petrol: 15.44,
			diesel: 16.088,
		},
	},
	2023: {
		"01": {
			petrol: 16.392,
			diesel: 17.272,
		},
		"02": {
			petrol: 16.776,
			diesel: 16.432,
		},
		"03": {
			petrol: 17.856,
			diesel: 16.992,
		},
		"04": {
			petrol: 17.976,
			diesel: 16.584,
		},
		"05": {
			petrol: 16.352,
			diesel: 15.44,
		},
		"06": {
			petrol: 17.608,
			diesel: 15.64,
		},
		"07": {
			petrol: 17.504,
			diesel: 15.776,
		},
		"08": {
			petrol: 18.336,
			diesel: 16.992,
		},
		"09": {
			petrol: 18.32,
			diesel: 17.288,
		},
		10: {
			petrol: 18.376,
			diesel: 18.784,
		},
		11: {
			petrol: 17.696,
			diesel: 16.992,
		},
		12: {
			petrol: 16.92,
			diesel: 16.792,
		},
	},
	2024: {
		"01": {
			petrol: 20.44,
			diesel: 20.1,
		},
		"02": {
			petrol: 21.92,
			diesel: 21.96,
		},
		"03": {
			petrol: 21.35,
			diesel: 21.05,
		},
		"04": {
			petrol: 22.44,
			diesel: 21.42,
		},
		"05": {
			petrol: 23.28,
			diesel: 21.66,
		},
		"06": {
			petrol: 23.41,
			diesel: 21.77,
		},
		"07": {
			petrol: 22.56,
			diesel: 21.04,
		},
		"08": {
			petrol: 21.98,
			diesel: 20.29,
		},
		// estimates for 2024
		"09": {
			petrol: 21.98,
			diesel: 20.29,
		},
		10: {
			petrol: 21.98,
			diesel: 20.29,
		},
		11: {
			petrol: 21.98,
			diesel: 20.29,
		},
		12: {
			petrol: 21.98,
			diesel: 20.29,
		},
	},
}

function f_petrol(amount, AccountID, period, year) {
	let liters = amount / fuelPrices[year][period]["petrol"]
	let kwh = liters * 9.61
	let co2e_scope1 = liters * 2.19
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61 //WTT
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		kwh,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
	}
}

function f_diesel(amount, AccountID, period, year) {
	let liters = amount / fuelPrices[year][period]["diesel"]
	let kwh = liters * 10.96
	let co2e_scope1 = liters * 2.51233
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	// let non_renewables = kwh
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
		kwh,
	}
}

function f_fuel(amount, AccountID, period, year) {
	let liters = amount / ((fuelPrices[year][period]["diesel"] + fuelPrices[year][period]["petrol"]) / 2)
	let kwh = (liters * (10.96 + 9.61)) / 2
	let co2e_scope1 = liters * ((2.19 + 2.51233) / 2)
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		kwh,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
	}
}

function f_energy(amount, AccountID, period, year) {
	let liters = amount / (fuelPrices[year][period]["diesel"] - 2)
	// Gas Oil
	let kwh = liters * 10.54
	let co2e_scope1 = liters * 2.75857
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	// transaction.scope_1 = co2e_scope1/1000
	// transaction.scope_3 = co2e_scope3/1000
	// transaction.liter = liters
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		kwh,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
	}
}

function f_otherFuel(amount, AccountID, period, year) {
	let liters = amount / (fuelPrices[year][period]["diesel"] - 2)
	let kwh = liters * 10.3
	// Fuel Oil
	let co2e_scope1 = liters * 3.1
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	// transaction.scope_1 = co2e_scope1/1000
	// transaction.scope_3 = co2e_scope3/1000
	// transaction.liter = liters
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		kwh,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
	}
}

function f_oil(amount, AccountID, period, year) {
	let liters = amount / (fuelPrices[year][period]["diesel"] - 2)
	let kwh = liters * 10.3
	// let non_renewables = kwh
	// Fuel Oil
	let co2e_scope1 = liters * 3.1
	let co2e_scope2 = 0.0
	let co2e_scope3 = liters * 0.61
	// transaction.scope_1 = co2e_scope1/1000
	// transaction.scope_3 = co2e_scope3/1000
	// transaction.liter = liters
	let mobile = 0
	let stationaryCombustion = 0
	let non_renewables = 0
	let renewables = 0
	let accountID = Number(AccountID)
	if (accountID < 7000) {
		stationaryCombustion = kwh
	} else {
		mobile = kwh
	}
	// transaction.scope_1 = co2e_scope1/1000
	// transaction.scope_3 = co2e_scope3/1000
	// transaction.liter = liters
	return {
		scope_1: co2e_scope1 / 1000.0,
		scope_2: co2e_scope2 / 1000.0,
		scope_3: co2e_scope3 / 1000.0,
		liter: liters,
		kwh,
		non_renewables,
		mobile,
		stationaryCombustion,
		renewables,
	}
}

function f_electricity(amount, AccountID, period, year) {
	// Location based factors included directly into f_electricity()
	let wttTD = 0.********
	let wttG = 0.0027461

	// NVE circa 300 grams per kwh
	let euMix = 0.3

	// Carbon analytics api
	let norway = 0.************

	let kwh = amount / (price[year][period] / 100.0)

	let scope2_location = (kwh * co2_mix[year]["NO"]["co2"]) / 1000
	let scope2_market = (kwh * co2_mix[year]["NO"]["residual"]) / 1000
	let consumption_based = (kwh * co2_mix[year]["NO"]["consumption"]) / 1000
	let scope3 = (kwh * (wttTD + wttG)) / 1000

	let renewables = kwh * co2_mix[year]["NO"]["renewables"]
	let non_renewables = kwh * co2_mix[year]["NO"]["fossil"]

	let nuclear = kwh * co2_mix[year]["NO"]["nuclear"]

	//let scope2 = kwh * norway;
	//let scope2EU = kwh * euMix;
	//let scope3 = kwh * (wttTD + wttG);
	//let totalFootprint = kwh * (wttTD + wttG + norway);
	//let totalFootprintEU = kwh * (wttTD + wttG + euMix);
	// All numbers are in kilos except kwh.

	return {
		kwh,
		scope2_location,
		scope_1: 0,
		scope_2: scope2_market,
		scope_3: scope3,
		location_based: scope2_location,
		market_based: scope2_market,
		consumption_based,
		renewables,
		nuclear,
		mobile: 0,
		stationaryCombustion: 0,
		non_renewables,
	}
}

function f_water(amount) {
	// Price Oslo kommune = 423 + 16,77m3 + 16,77m3
	let m3 = amount / 30
	// water
	let supply = m3 * 0.149
	let treatment = m3 * 0.272
	let scope_3 = supply + treatment
	// transaction.scope_3 = scope3/1000
	// transaction.m3 = m3

	return {
		scope_1: 0.0,
		scope_2: 0.0,
		scope_3: scope_3 / 1000.0,
	}
}

function f_food(amount) {
	// From the IOC reporting framework on food
	co2_per_meal = 4.7
	// Price is just a guesstimate for now
	price_per_meal = 200
	meals = amount / price_per_meal
	let scope_3 = meals * co2_per_meal

	return {
		scope_1: 0.0,
		scope_2: 0.0,
		scope_3: scope_3 / 1000.0,
	}
}

function f_food2(amount) {
	// From the IOC reporting framework on food
	co2_per_meal = 4.7
	// Price is just a guesstimate for now
	price_per_meal = 200
	meals = amount / price_per_meal
	let scope_3 = meals * co2_per_meal

	return {
		scope_1: 0.0,
		scope_2: 0.0,
		scope_3: scope_3 / 1000.0,
	}
}

function f_heat(amount, AccountID, period, year) {
	// LCA 3 - https://www.fortum.no/om-oss/miljo-samfunnsansvar/miljoarbeid-i-fortum-oslo-varme/nokkeltall-miljo-og-klima-2020
	let fortum = 0.004

	// kwh is calculated based on electricity price as these are following each other
	let kwh = amount / (price[year][period] / 100)
	let non_renewables = kwh
	let renewables = kwh

	let scope2 = kwh * fortum
	// WTT from heat - UK Government GHG Conversion Factors for Company Reporting
	let wttHeat = 0.007 + 0.007
	let scope3 = kwh * wttHeat
	let totalFootprint = scope2 + scope3

	// let totalFootprint = scope2 + scope3;

	return {
		scope_1: 0.0,
		scope_2: scope2 / 1000.0,
		scope_3: scope3 / 1000.0,
		kwh: kwh,
		non_renewables,
		renewables,
		totalFootprint,
	}
}

function f_default(amount) {
	return {
		scope_1: 0.0,
		scope_2: 0.0,
		scope_3: 0.0,
	}
}

const functionData = {
	f_petrol: f_petrol,
	f_diesel: f_diesel,
	f_fuel: f_fuel,
	f_energy: f_energy,
	f_otherFuel: f_otherFuel,
	f_oil: f_oil,
	f_electricity: f_electricity,
	f_water: f_water,
	f_heat: f_heat,
	f_food: f_food,
	f_food2: f_food2,
	f_default: f_default,
}

function getFunction(functionName) {
	if (functionName in functionData) {
		return functionData[functionName]
	} else {
		return functionData["f_default"]
	}
}

exports = function (arg) {
	const functionName = arg.functionName
	const Amount = arg.Amount
	let period = arg.period
	let AccountID = arg.AccountID
	let year = "2023"
	console.log("ARGS===", JSON.stringify(arg))
	if (period.length == 1) {
		period = "0" + period
	}
	if (arg.year) {
		year = arg.year
	}
	const func = getFunction(functionName)
	const res = func(Amount, AccountID, period, year)
	console.log("RES===", JSON.stringify(res))
	return res
}
