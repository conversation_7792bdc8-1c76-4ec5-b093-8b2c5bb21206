/* eslint-disable import/no-unresolved */
import React, { useState } from "react"
import { useSelector } from "react-redux"

import { useRealmApp } from "../../../realm/RealmAppProvider"
import useFullPageLoader from "../../loader/useFullPageLoader"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

export default function Grouping({ t }) {
	const [accessRegistrationNumber, setAccessRegistrationNumber] = useState("")

	const app = useRealmApp()

	const Toast = useToast()

	const [loader, showLoader, hideLoader] = useFullPageLoader()

	const currentOrganization = useSelector((state) => state.user.currentOrganization)

	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""

	const handleGrantAccess = async () => {
		if (RegistrationNumber === accessRegistrationNumber) {
			Toast("error", t("Company cannot grant access to itself in this manner."))
			return
		}

		if (!RegistrationNumber) {
			Toast("error", t("Please register your company first."))
			return
		}

		if (!accessRegistrationNumber) {
			Toast("error", t("Please enter the registration number of the company you want to grant access to."))
			return
		}

		showLoader()

		const { statusCode } = await app.grantAccess({
			companyOwnRegistrationNumber: RegistrationNumber,
			accessRegistrationNumber,
		})

		if (statusCode === 404) {
			Toast("error", t("organization:company_not_found"))
		}
		if (statusCode === 200) {
			Toast("success", t("Access granted"))
		}

		hideLoader()
	}

	return (
		<div className='cl-component cl-user-profile'>
			<div className='cl-main mt-4'>
				<div className='cl-user-profile-card cl-themed-card'>
					<div className='cl-titled-card-list'>
						<h4 className='font-extrabold text-2xl'>{t("Grant Access")}</h4>

						<div className='w-full mt-5'>
							<Input
								label={t("common:RegistrationNumber")}
								type='text'
								value={accessRegistrationNumber}
								handleChange={(e) => setAccessRegistrationNumber(e.target.value)}
							/>
						</div>

						<div className='w-full mt-3'>
							<div className='flex justify-end'>
								<Button title={t("GrantAccess")} handleClick={() => handleGrantAccess()} />
							</div>
						</div>
					</div>
				</div>
			</div>
			{loader}
		</div>
	)
}
