/* eslint-disable import/no-unresolved */
import React, { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { co2_mix } from "../../scope2/constants/electricTransportation"

import Button from "@/components/ui/Button" // Assuming alias setup
import Input from "@/components/ui/Input" // Assuming alias setup
import { useToast } from "@/hooks"
import { FACTORS_DATA_COMBUSTION } from "@/pages/transaction/editdetails/scope1/constants/combustion"
import { FUEL_TYPE_LIST, VEHICLE_TYPE_LIST } from "@/pages/transaction/editdetails/scope1/constants/vehicles"

function Scope3Category4VTContent(props) {
	const {
		category4VTRows = [],
		updateCategory4VTEmission,
		PeriodYear, // Default to current year if not provided
	} = props

	const { t } = useTranslation("transactions")
	const distanceInputRef = useRef()

	const toast = useToast()

	// Local state for the "Add New" form inputs
	const [vehicleType, setVehicleType] = useState(null)
	const [vehicle, setVehicle] = useState("")
	const [fuel, setFuel] = useState("")
	const [fuelType, setFuelType] = useState(null)
	const [distance, setDistance] = useState(0.0)
	const [distanceUnit, setDistanceUnit] = useState("km") // Default unit
	const [economy, setEconomy] = useState("") // Use empty string to differentiate unset from 0
	const [economyUnit, setEconomyUnit] = useState("L/100km") // Default unit

	// Handle Vehicle Type change
	const handleVehicleTypeChange = (selectedOption) => {
		setVehicleType(selectedOption)
		setVehicle(selectedOption.label)
		// Update economy unit based on vehicle type if needed (e.g., kWh/100km for electric)
		if (selectedOption?.value?.toLowerCase().includes("electric")) {
			setEconomyUnit("kWh/100km")
		} else {
			setEconomyUnit("L/100km") // Default for others
		}
		distanceInputRef.current?.focus()
	}

	// Handle Fuel Type change
	const handleFuelTypeChange = (selectedOption) => {
		setFuelType(selectedOption)
		setFuel(selectedOption.label)
		// Update economy unit based on fuel type if needed
		if (selectedOption?.value === "electric") {
			setEconomyUnit("kWh/100km")
		} else {
			setEconomyUnit("L/100km")
		}
		distanceInputRef.current?.focus()
	}

	// Adds the new row data (from local state) to the main list (parent state)
	const addCategory4VTRow = (newRowData) => {
		const newCategory4VTRowsList = [...category4VTRows, newRowData]

		updateCategory4VTEmission(newRowData, false, newCategory4VTRowsList, 4) // false indicates addition

		// Reset form fields
		setVehicleType(null)
		setFuelType(null)
		setDistance(0.0)
		setEconomy("")
		setDistanceUnit("km")
		setEconomyUnit("L/100km")
	}

	// Calculates emission and triggers adding the row
	const handleAddEmission = () => {
		// --- Validation ---
		if (!vehicleType) {
			toast("error", t("vehicle_type_error"))
			return
		}
		if (!fuelType) {
			toast("error", t("fuel_type_error"))
			return
		}
		const numDistance = parseFloat(distance)
		if (!(numDistance >= 0)) {
			toast("error", t("distance_error"))
			return
		}
		const numEconomy = economy === "" ? null : parseFloat(economy) // Allow empty economy unless required
		if (vehicleType.value === "heavytruck" && numEconomy === null) {
			toast("error", t("economy_heavy_truck_error"))
			return
		}
		if (numEconomy !== null && numEconomy < 0) {
			toast("error", t("economy_negative_error"))
			return
		}

		// --- Calculation ---
		let calculatedScope1 = 0.0
		let calculatedScope2 = 0.0
		let calculatedScope3 = 0.0
		let calculatedKwh = 0.0
		let calculatedRenewable = 0.0
		let calculatednon_renewable_energy = 0.0
		let calculatedMobileComb = 0.0

		let combustionMetric = ""
		if (fuelType.value === "petrol") {
			combustionMetric = "PetrolForecourt"
		} else if (fuelType.value === "diesel") {
			combustionMetric = "DieselForecourt"
		} else if (fuelType.value === "hybrid") {
			// Assuming fuelType object might have a 'name' property like in original function
			if (fuelType.name === "Petrol") combustionMetric = "PetrolForecourt"
			else if (fuelType.name === "Diesel") combustionMetric = "DieselForecourt"
			else combustionMetric = "PetrolForecourt" // Default hybrid if name unknown
		}

		if (fuelType.value !== "electric") {
			if (combustionMetric && FACTORS_DATA_COMBUSTION[combustionMetric]) {
				let effectiveEconomy = numEconomy !== null ? numEconomy : vehicleType.economy // Use default economy from vehicleType if needed
				if (fuelType.value === "hybrid") {
					// Apply hybrid adjustment if applicable (using default economy)
					effectiveEconomy = (vehicleType.economy || 0) * 0.85
				}

				if (effectiveEconomy > 0) {
					const factorData = FACTORS_DATA_COMBUSTION[combustionMetric]
					// Calculate based on L/100km economy
					calculatedScope1 = (factorData.scope1 || 0) * numDistance * (effectiveEconomy / 100)
					calculatedScope3 = (factorData.scope3 || 0) * numDistance * (effectiveEconomy / 100)
					let liters = (effectiveEconomy / 100) * numDistance
					calculatedMobileComb = liters * (factorData.kwh || 0) // Assuming kwh here represents energy content factor
				}
			} else {
				console.warn("Combustion factors not found for metric:", combustionMetric)
			}
		} else {
			// Electric Vehicle
			let electricFactor = 0
			// Use provided economy if available, otherwise default from vehicleType
			let effectiveElEconomy = numEconomy !== null ? numEconomy : vehicleType.elEconomy

			if (effectiveElEconomy) {
				// The original code subtracts 0.014 - unclear why, replicating for now
				electricFactor = effectiveElEconomy - 0.014
				if (electricFactor < 0) electricFactor = 0 // Ensure non-negative factor

				calculatedKwh = (numDistance * electricFactor) / 100 // Assuming economy is kWh/100km

				// Renewable/Non-renewable split (hardcoded 98%/2% in original)
				calculatedRenewable = calculatedKwh * 0.98
				calculatednon_renewable_energy = calculatedKwh * 0.02

				// Scope 2 Calculation (depends on co2_mix data structure)
				const yearStr = String(PeriodYear.value)
				if (co2_mix[yearStr] && co2_mix[yearStr]["NO"] && co2_mix[yearStr]["NO"]["consumption"] !== undefined) {
					calculatedScope2 = calculatedKwh * co2_mix[yearStr]["NO"]["consumption"]
				} else {
					console.warn("CO2 mix data not found for year/region:", yearStr, "NO")
				}

				// Scope 3 for electric (hardcoded 0.014 in original - unclear source/unit)
				calculatedScope3 = 0.014 * numDistance // Assuming this factor is per km? Needs clarification.
			} else {
				console.warn("Electric economy factor not found for vehicle type:", vehicleType.label)
			}
		}

		// --- Create Row Object ---
		const newCategory4VTRow = {
			vehicleType: vehicle,
			fuelType: fuel,
			distance: numDistance,
			distanceUnit: distanceUnit,
			economy: numEconomy, // Store the number or null
			economyUnit: economyUnit,
			scope1: calculatedScope1,
			scope2: calculatedScope2,
			scope3: calculatedScope3, // Store calculated S3
			kwh: calculatedKwh,
			renewable_energy: calculatedRenewable,
			non_renewable_energy: calculatednon_renewable_energy,
			mobileCombustion: calculatedMobileComb,
		}

		addCategory4VTRow(newCategory4VTRow)
		return { success: true }
	}

	// Deletes a row from the list by index
	const deleteCategory4VTRow = (index) => {
		const rowToDelete = category4VTRows[index]
		const newCategory4VTRowsList = [...category4VTRows]
		newCategory4VTRowsList.splice(index, 1)

		updateCategory4VTEmission(rowToDelete, true, newCategory4VTRowsList, 4) // true indicates deletion
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{/* Display existing/saved rows */}
			{category4VTRows.map((vehicleItem, index) => (
				// Using the display structure from the original component's 'isSaved' block
				<div key={index}>
					<div className='grid grid-cols-6 gap-4 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("VehicleType")}</label>
							<div>{vehicleItem.vehicleType || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("FuelType")}</label>
							<div>{vehicleItem.fuelType || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Distance")}</label>
							<div className='flex relative'>
								<span>{vehicleItem.distance}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{vehicleItem.distanceUnit || "km"}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Economy")}</label>
							<div className='flex relative'>
								<span>{vehicleItem.economy ?? "--"}</span>
								<span className='text-nowrap custom-span-unit-value-save'>
									{vehicleItem.economyUnit ? vehicleItem.economyUnit.replace("per", "/") : ""}
								</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								{/* Summing up calculated scopes for display */}
								<span>
									{Number((vehicleItem.scope1 || 0) + (vehicleItem.scope2 || 0) + (vehicleItem.scope3 || 0)).toFixed(2)}
								</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>
						<div className='delete-icon !items-center'>
							<span aria-hidden onClick={() => deleteCategory4VTRow(index)} style={{ cursor: "pointer" }}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-5 gap-5 my-2'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"vehicle-type-new"}>
						{t("VehicleType")}
						<span className='text-red-600'>*</span>
					</label>
					<Select
						id={"vehicle-type-new"}
						value={vehicleType}
						onChange={handleVehicleTypeChange}
						options={VEHICLE_TYPE_LIST}
						placeholder={t("Select...")}
						maxMenuHeight={150}
					/>
				</div>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"fuel-vehicle-new"}>
						{t("FuelType")}
						<span className='text-red-600'>*</span>
					</label>
					<Select
						id={"fuel-vehicle-new"}
						value={fuelType}
						onChange={handleFuelTypeChange}
						options={FUEL_TYPE_LIST}
						placeholder={t("Select...")}
						maxMenuHeight={150}
					/>
				</div>
				<div>
					<Input
						label={t("Distance")}
						type={"number"}
						required
						placeholder={t("Enter distance")}
						labelColor='text-sky-500'
						value={distance}
						ref={distanceInputRef}
						unit={distanceUnit}
						handleChange={(e) => setDistance(e.target.value)}
						handleFocus={handleFocus} // Corrected prop name
						min='0'
						step='any'
					/>
				</div>
				<div>
					<Input
						label={t("Economy")}
						type={"number"}
						labelColor='text-sky-500'
						value={economy}
						handleChange={(e) => setEconomy(e.target.value)}
						handleFocus={handleFocus} // Corrected prop name
						placeholder={vehicleType?.value === "heavytruck" ? t("Required") : t("Optional")}
						unit={economyUnit.replace("per", "/")}
						min='0'
						step='any'
					/>
				</div>
				<div className='self-end mb-1'>
					<Button handleClick={handleAddEmission} title={`${t("Add")} | +`} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default Scope3Category4VTContent
