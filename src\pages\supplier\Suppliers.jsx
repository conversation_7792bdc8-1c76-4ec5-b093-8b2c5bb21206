import "../../index.css"
import { <PERSON><PERSON><PERSON>, <PERSON>10n } from "@syncfusion/ej2-base"
import {
	GridComponent,
	ColumnsDirective,
	ColumnDirective,
	Page,
	Toolbar,
	Edit,
	Inject,
	Filter,
	Group,
	Resize,
	Sort,
	Aggregate,
} from "@syncfusion/ej2-react-grids"
// This is imported because we need to update supplier list when new supplier is added
import { useQueryClient } from "@tanstack/react-query"
import React, { useEffect, useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"

import green_flag from "../../assets/green_flag.png"
import orange_flag from "../../assets/orange_flag.png"
import red_flag from "../../assets/red_flag.png"
import AddReportingEntityModal from "../../components/AddReportingEntityModal.component"
import Spinner from "../../components/ui/Spinner"
import {
	useToast,
	useGetNaceQuery,
	useGetSupp<PERSON>,
	//useInsertSupplierMutation,
	useGetSuppliersList,
	//useUpdateSupplierMutation,
} from "../../hooks"
import { useRealmApp } from "../../realm/RealmAppProvider"
import { valueFormat } from "../../services/helper"
import useFullPageLoader from "../loader/useFullPageLoader"

import { SuppliersDialogForm } from "./SuppliersDialogForm"

/*
reference:
https://ej2.syncfusion.com/react/demos/#/material/grid/dialog-template
https://ej2.syncfusion.com/react/demos/#/material/grid/custom-binding
https://ej2.syncfusion.com/react/demos/#/material/grid/filter-menu
*/

function Suppliers(props) {
	const app = useRealmApp()

	const suppliersFetched = useRef(false)

	const queryClient = useQueryClient()

	// Get the netlify ID(it will be used to fetch suppliers)
	const { t, i18n } = useTranslation(["suppliers", "common"])
	const currentOrganization = useSelector((state) => state.user.currentOrganization)

	const isCompanyRegistered = useSelector((state) => state.company)

	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""
	const reportingYear = useSelector((state) => state.reporting)
	const input = {
		RegistrationNumber: RegistrationNumber ? RegistrationNumber : "",
		year: reportingYear || "2022",
	}

	const Toast = useToast()

	const suppliersList = useGetSuppliersList(app, { RegistrationNumber })

	const getSuppliers = useGetSuppliers(app, input)

	const getNace = useGetNaceQuery(app)

	const [supplierData, setSupplierData] = useState([])

	//const [emissionSum, setEmissionSum] = useState(0)

	const gridRef = React.useRef(null)

	const [loader, showLoader, hideLoader] = useFullPageLoader()

	//const insertSupplierMutation = useInsertSupplierMutation(app)

	//const updateSupplier = useUpdateSupplierMutation(app)

	const dialogTemplate = (props) => {
		return (
			<SuppliersDialogForm
				{...props}
				locale={i18n.language}
				t={t}
				language={i18n.language}
				naceData={getNace?.data?.status ? getNace?.data?.data : []}
				reportingYear={reportingYear}
			/>
		)
	}

	const imageTemplate = (props) => {
		const statusData = {
			1: green_flag,
			2: orange_flag,
			3: red_flag,
		}
		const displayNameForFlag = {
			0: "Info",
			1: "Ok",
			2: "Processed",
			3: "Warning",
		}
		return (
			<div className='flex items-center'>
				<img src={statusData[props.Status]} alt={props.Status} style={{ marginTop: "0px", height: "20px" }} />
				{displayNameForFlag[props.Status]}
			</div>
		)
	}

	// syncfusion react grid configuration
	const toolbarOptions = ["Edit", "Add"]

	L10n.load({
		en: {
			grid: {
				Edit: "Edit",
				Add: "Add",
			},
		},
		no: {
			grid: {
				Edit: "Rediger",
				Add: "Legg til",
			},
		},
		// Add translations for other languages if needed
	})

	const editSettings = {
		allowEditing: true,
		allowAdding: RegistrationNumber ? true : false,
		mode: "Dialog",
		template: dialogTemplate,
	}
	const pageSettings = { pageCount: 5, pageSize: 30 }
	const filterSettings = { type: "Excel" }
	const removeMVAextension = (num) => {
		if (typeof num === "string" || num instanceof String) {
			// Remove all the whitespace.
			num = num.replace(/\s/g, "")
			// If num has "MVA" at the end of string then remove it.
			const len = num.length
			/* eslint eqeqeq: 0 */
			if (len >= 3 && num.substr(len - 3) == "MVA") {
				return num.substr(0, len - 3)
			}
			return num
		} else {
			return ""
		}
	}
	const RegistrationNumberTemplate = (props) => {
		try {
			const url = `https://w2.brreg.no/enhet/sok/detalj.jsp?orgnr=${removeMVAextension(props.RegistrationNumber)}`
			return (
				/* eslint react/jsx-no-target-blank: 0 */
				<a href={url} target='_blank' className='text-sky-600' rel='noreferrer'>
					{props.RegistrationNumber}
				</a>
			)
		} catch (error) {
			// handle error
		}
	}
	const formatEmissions = {
		maximumFractionDigits: 0,
	}
	const EmissionTemplate = (props) => {
		const locale = i18n.language === "en" ? "en-EN" : "no-NO"
		return <span>{valueFormat(Math.round(props.Emission * 1000), locale, "number")} kg</span>
	}
	// const footerEmissionSum = () => {
	// 	return <span>Sum: {parseFloat(emissionSum).toFixed(0)} Tons</span>
	// }

	const updateValues = (prevTransactions, newTransactions, factor, NaceCode, key, SupplierName) => {
		// Create a new object instead of copying the cached transactions.
		// This ensures that changes made to the new object do not affect the cached transactions.
		const updatedTransactions = [...prevTransactions]

		try {
			// Find the indexes of updated transactions in cached transactions.
			const indexes = newTransactions.map(({ _id }) =>
				updatedTransactions.findIndex((trans) => trans._id.toString() === _id)
			)

			// Update transactions on all those indexes which we have got in the previous step.
			for (let i = 0; i < indexes.length; i++) {
				const index = indexes[i]
				if (index >= 0) {
					const updatedData = {
						...updatedTransactions[index],
						supplierFactor: factor,
						NaceCode: NaceCode,
						SupplierName: SupplierName,
					}
					if (updatedTransactions[index].Status === 2) {
						if (key === 3) updatedData["Scope_3"] = newTransactions[i].emission
						if (key === 2) updatedData["Scope_2"] = newTransactions[i].emission
						if (key === 1) updatedData["Scope_1"] = newTransactions[i].emission
					}
					updatedTransactions.splice(index, 1, updatedData)
				}
			}

			// Return updated data.
			return updatedTransactions
		} catch (error) {
			console.error(error)
			// Return the original data in case of an error.
			return prevTransactions
		}
	}

	const updateTransactionCachedData = async (transactions, factor, NaceCode, SupplierName) => {
		try {
			// Fetch all transaction data from cache
			// const [scope1Transactions, scope2Transactions, scope3Transactions] = await Promise.all(
			//   getTransactionsQueryKeys.map(async (key) => await queryClient.getQueryData(key))
			// )

			const scope1Transactions = await queryClient.getQueryData([
				"getTransactions",
				{
					RegistrationNumber,
					Scope: 1,
					year: reportingYear,
				},
			])
			const scope2Transactions = await queryClient.getQueryData([
				"getTransactions",
				{
					RegistrationNumber,
					Scope: 2,
					year: reportingYear,
				},
			])

			const scope3Transactions = await queryClient.getQueryData([
				"getTransactions",
				{
					RegistrationNumber,
					Scope: 3,
					year: reportingYear,
				},
			])
			// Group transactions by scope type
			const groupedTransactions = transactions.reduce((acc, trans) => {
				if (!acc[trans.type]) {
					acc[trans.type] = []
				}
				acc[trans.type].push(trans)
				return acc
			}, {})

			// Update cached data for each scope
			const scopesToUpdate = [
				{ key: 1, data: scope1Transactions },
				{ key: 2, data: scope2Transactions },
				{ key: 3, data: scope3Transactions },
			]

			scopesToUpdate.forEach(async ({ key, data }) => {
				if (groupedTransactions[key] && data) {
					const updatedData = updateValues(data, groupedTransactions[key], factor, NaceCode, key, SupplierName)
					await queryClient.setQueryData(
						["getTransactions", { RegistrationNumber, Scope: key, year: reportingYear }],
						updatedData
					)
				}
			})
		} catch (error) {
			console.error(error)
		}
	}

	const getSupplierAndIndusryIntensity = async (NaceCode, intensity, SupplierCo2IntensitySaved) => {
		try {
			const NaceData = getNace?.data?.data || []
			// eslint-disable-next-line array-callback-return
			const industry = NaceData.filter((nace) => {
				if (nace.code === NaceCode) {
					return nace
				}
			})

			let supplierCarbonIntensity = {}
			let industryCarbonIntensity = {}

			if (industry.length > 0) {
				supplierCarbonIntensity =
					SupplierCo2IntensitySaved !== null ? { ...SupplierCo2IntensitySaved } : { ...industry[0].co2Intensity }
				industryCarbonIntensity = { ...industry[0].co2Intensity }

				if (supplierCarbonIntensity !== Number(intensity)) {
					for (let year = reportingYear; year <= Number(reportingYear); year++) {
						supplierCarbonIntensity[year] = Number(intensity)
					}
				}
			} else if (industry.length == 0 && intensity) {
				supplierCarbonIntensity = { 2020: 21.55, 2021: 21.27, 2022: 20.55, 2023: 19.48, 2024: 18.66 }
				industryCarbonIntensity = { 2020: 21.55, 2021: 21.27, 2022: 20.55, 2023: 19.48, 2024: 18.66 }
				for (let year = reportingYear; year <= 2024; year++) {
					supplierCarbonIntensity[year] = Number(intensity)
				}
			}

			return { supplierCarbonIntensity, industryCarbonIntensity }
		} catch (error) {
			// handle error
		}
	}

	const removeSupplierFromGrid = (supplierID) => {
		try {
			const index = supplierData.findIndex((supplier) => supplier.SupplierID == supplierID)
			if (index > -1) {
				let newSupplierData = [...supplierData]
				newSupplierData.splice(index, 1)
				setSupplierData(newSupplierData)
			}
		} catch (error) {}
	}

	const addNewSupplier = async (args) => {
		try {
			const { supplierCarbonIntensity, industryCarbonIntensity } = await getSupplierAndIndusryIntensity(
				args.data?.NaceCode ?? "",
				args.data.SupplierCo2Intensity,
				null
			)

			let insertSupplierData = {
				RegistrationNumber: args.data?.RegistrationNumber ? args.data.RegistrationNumber : "",
				Name: args.data?.Name ? args.data.Name : "",
				SupplierID: args.data?.SupplierID ? args.data?.SupplierID : "",
				ClosingCreditBalance: args.data?.ClosingCreditBalance ? args.data.ClosingCreditBalance : "0",
				NaceCode: args.data?.NaceCode ?? "",
				NaicsCode: args.data?.NaicsCode ?? "",
				Industry: args.data?.Industry ?? "",
				Status: parseInt(args.data?.Status),
				Notes: args.data?.Notes ? args.data.Notes : "",
				Emission: 0.0,
				Amount: 0.0,
				IndustryCo2Intensity: industryCarbonIntensity,
				SupplierCo2Intensity: supplierCarbonIntensity,
				Contact: {
					ContactPerson: {
						FirstName: args.data?.Contact.ContactPerson.FirstName ?? "",
						LastName: args.data?.Contact.ContactPerson.LastName ?? "",
					},
					Email: args.data?.Contact && args.data?.Contact?.Email ? args.data.Contact.Email : "",
					Telephone: args.data?.Contact && args.data?.Contact?.Telephone ? args.data.Contact.Telephone : "",
				},
			}

			let sendData = {
				RegistrationNumber: RegistrationNumber,
				supplier: insertSupplierData,
			}

			showLoader()
			const result = await app.insertNewSupplier(sendData)
			hideLoader()
			if (result && result.status === "done") {
				suppliersList.refetch()

				Toast("success", t("new_supplier_added_message"))
			} else {
				// remove newly added supllier from the grid
				removeSupplierFromGrid(insertSupplierData.SupplierID)

				Toast("error", t("error_adding_supplier_message"))
			}
		} catch (error) {
			Toast("error", t("error_adding_supplier_message"))
		}
	}

	const updateCachedSuppliers = async (index, supplierIntensity) => {
		let newSuppliers = [...supplierData]
		newSuppliers[index].SupplierCo2Intensity = supplierIntensity
		try {
			queryClient.setQueryData(["getSuppliers", { RegistrationNumber, year: reportingYear }], newSuppliers)
		} catch (error) {}
	}

	const editSupplier = async (args) => {
		try {
			let index = supplierData.findIndex((supplier) => supplier.SupplierID === args.data.SupplierID)
			const { supplierCarbonIntensity, industryCarbonIntensity } = await getSupplierAndIndusryIntensity(
				args.data?.NaceCode ?? "",
				args.data.SupplierCo2Intensity,
				getSuppliers?.data[index].SupplierCo2Intensity
			)

			if (index !== -1) {
				const { ...restFields } = args.data

				if (!restFields.Industry) restFields.Industry = ""
				if (!restFields.Notes) restFields.Notes = ""

				let newData = {
					...restFields,
					year: reportingYear,
					companyRegistrationNumber: RegistrationNumber,
					SupplierID: supplierData[index].SupplierID,
					Status: parseInt(args.data.Status),
					SupplierCo2Intensity: supplierCarbonIntensity,
					IndustryCo2Intensity: industryCarbonIntensity,
				}

				const previousSupplierCo2Intensity =
					typeof args.previousData.SupplierCo2Intensity === "object"
						? args.previousData.SupplierCo2Intensity[reportingYear]
						: args.previousData.SupplierCo2Intensity

				if (args.data.SupplierCo2Intensity != previousSupplierCo2Intensity) {
					newData["isSupplierFactorChanged"] = true
				} else {
					newData["isSupplierFactorChanged"] = false
				}

				if (newData.Name !== args.previousData.Name) {
					newData["isSupplierNameChanged"] = true
				} else {
					newData["isSupplierNameChanged"] = false
				}

				// to track wheter supplier has updated its factor or not so if user has already changed it and we have updated
				// it in db then it will remain as true else it will check for isSupplierFactorChanged
				const { isSupplierFactorUpdated } = supplierData[index]
				newData["isSupplierFactorUpdated"] = isSupplierFactorUpdated ? true : newData["isSupplierFactorChanged"]

				delete newData.numberOfTransactions

				showLoader()

				const data = await app.updateSupplier(newData)

				hideLoader()

				if (data?.status === "done") {
					const { Emission, transactions } = data

					updateCachedSuppliers(index, supplierCarbonIntensity)

					if (newData["isSupplierFactorChanged"] || newData["isSupplierNameChanged"]) {
						updateTransactionCachedData(
							transactions,
							newData.SupplierCo2Intensity[reportingYear] || newData.SupplierCo2Intensity,
							newData.NaceCode,
							newData.Name
						)
					}
					let updatedSuppliers = [...supplierData]

					if (index != -1) {
						const newSupplier = { ...supplierData[index], ...newData }
						newSupplier.SupplierCo2intensity = supplierCarbonIntensity
						if (Emission != 0) {
							newSupplier.Emission = Emission
						}
						updatedSuppliers.splice(index, 1, newSupplier)
						//gridRef.current.refresh()
					}

					setSupplierData([...updatedSuppliers])
					suppliersList.refetch()
					Toast("success", t("supplier_updated_message"))
				} else {
					Toast("error", t("error_updating_supplier_message"))
				}
			}
		} catch (error) {
			Toast("error", t("error_updating_supplier_message"))
		}
	}

	const actionComplete = async (args) => {
		try {
			if (args.requestType === "beginEdit" || args.requestType === "add") {
				if (Browser.isDevice) {
					args.dialog.height = window.innerHeight - 90 + "px"
					args.dialog.dataBind()
				}
			}

			// On any edit in the grid, update the suppliers in the database.
			// "args" containes all the necessary data regarding edit
			if (args.action === "edit" && args.requestType === "save") {
				// find the index 0f row which was edited.
				await editSupplier(args)
			}

			if (args.action === "add" && args.requestType === "save") {
				await addNewSupplier(args)
			}
		} catch (error) {
			// handle global error for the action complete
		}
	}

	// To remove certain fields from objects(ex. sometimes while calling graphql mutation we need to remove extra fields before sending data)
	const clearFields = (obj, fields) => {
		let newData = {}
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				if (typeof obj[key] == "object") {
					// if field value is object then recursively call the function to remove field
					newData[key] = clearFields(obj[key], fields)
				} else if (fields.indexOf(key) === -1) {
					// if current field name is not in 'fields' array then add it to new objects
					newData[key] = obj[key]
				}
			}
		}
		return newData
	}

	useEffect(() => {
		// To remove "__typename" field from coming graphql data
		const fixData = (newSupplierData) => {
			//let newEmissionSum = 0
			for (let i = 0; i < newSupplierData.length; i++) {
				newSupplierData[i].Emission = parseFloat(newSupplierData[i].Emission || 0.0)
				//newEmissionSum += newSupplierData[i].Emission
				const num = newSupplierData[i].RegistrationNumber
				newSupplierData[i].Contact.Email = newSupplierData[i].Contact.Email ? newSupplierData[i].Contact.Email : ""
				if (!(typeof num === "string" || num instanceof String)) {
					newSupplierData[i].RegistrationNumber = ""
				}
			}
			//setEmissionSum(newEmissionSum)
			/* eslint react-hooks/exhaustive-deps: 0 */
			return newSupplierData.map((supplierData) => clearFields(supplierData, ["__typename"]))
		}
		if (getSuppliers.data && getSuppliers.data?.length) {
			setSupplierData(fixData([...getSuppliers.data]))
			suppliersFetched.current = true
		} else {
			setSupplierData([])
			suppliersFetched.current = true
		}
	}, [getSuppliers.data])

	useEffect(() => {
		if (!getNace?.data?.data) getNace.refetch()
	}, [RegistrationNumber])

	useEffect(() => {
		return () => {
			getSuppliers.refetch()
		}
	}, [])

	if (
		((RegistrationNumber && supplierData.length === 0 && getSuppliers.isLoading) ||
			getSuppliers.isFetching ||
			getNace.isLoading ||
			!suppliersFetched.current) &&
		isCompanyRegistered
	) {
		return (
			<div className='mainContainer'>
				<Spinner />
			</div>
		)
	} else {
		return (
			<div>
				<div>
					<div>
						<GridComponent
							// grid configurations
							ref={gridRef}
							dataSource={supplierData}
							allowGrouping={true}
							toolbar={toolbarOptions}
							allowPaging={true}
							pageSettings={pageSettings}
							allowFiltering={true}
							filterSettings={filterSettings}
							editSettings={editSettings}
							actionComplete={actionComplete}
							allowResizing={true}
							allowSorting={true}
							locale={i18n.language}
						>
							<ColumnsDirective>
								{/* set grid columns */}

								{/* hide telephone field */}
								<ColumnDirective
									field='Contact.Telephone'
									headerText={t("Telephone")}
									minWidth='80'
									width='120'
									maxWidth='300'
									textAlign='center'
									visible={false}
								></ColumnDirective>

								{/* hide Note field */}
								<ColumnDirective
									field='Notes'
									headerText={t("Notes")}
									minWidth='80'
									width='120'
									maxWidth='300'
									textAlign='center'
									visible={false}
								></ColumnDirective>

								<ColumnDirective
									field='SupplierID'
									headerText={t("SupplierID")}
									minWidth='80'
									width='120'
									maxWidth='300'
									textAlign='center'
								></ColumnDirective>

								<ColumnDirective
									template={RegistrationNumberTemplate}
									field='RegistrationNumber'
									headerText={t("RegistrationNumber")}
									minWidth='80'
									width='100'
									maxWidth='300'
									textAlign='center'
								></ColumnDirective>

								<ColumnDirective
									field='Name'
									headerText={t("SupplierName")}
									minWidth='80'
									width='180'
									maxWidth='300'
									textAlign='left'
									isPrimaryKey={true}
								></ColumnDirective>

								<ColumnDirective
									field='Contact.ContactPerson.FirstName'
									headerText={t("FirstName")}
									minWidth='80'
									width='120'
									maxWidth='300'
									textAlign='left'
								></ColumnDirective>

								<ColumnDirective
									field='Contact.ContactPerson.LastName'
									headerText={t("LastName")}
									minWidth='80'
									width='120'
									maxWidth='300'
									textAlign='left'
								></ColumnDirective>

								<ColumnDirective
									field='Contact.Email'
									headerText={t("Email")}
									minWidth='80'
									width='100'
									maxWidth='300'
									textAlign='left'
								></ColumnDirective>

								<ColumnDirective
									field='NaceCode'
									headerText={t("NaceCode")}
									minWidth='80'
									width='80'
									maxWidth='300'
									textAlign='center'
								></ColumnDirective>

								<ColumnDirective
									field='Industry'
									headerText={t("Industry")}
									minWidth='80'
									width='160'
									maxWidth='300'
									textAlign='left'
								></ColumnDirective>

								<ColumnDirective
									field='Status'
									template={imageTemplate}
									headerText={t("Status")}
									filterTemplate={imageTemplate}
									filterItemTemplate={imageTemplate}
									minWidth='70'
									width='100'
									maxWidth='300'
									textAlign='center'
								></ColumnDirective>

								<ColumnDirective
									field='numberOfTransactions'
									headerText={t("#Transactions")}
									minWidth='90'
									format='N'
									width='120'
									maxWidth='300'
									textAlign='Center'
								></ColumnDirective>

								<ColumnDirective
									field='Emission'
									template={EmissionTemplate}
									headerText={t("TotalEmissions")}
									minWidth='80'
									type='number'
									format={formatEmissions}
									width='140'
									maxWidth='300'
									textAlign='right'
								></ColumnDirective>

								<ColumnDirective
									field='Amount'
									headerText={t("Total Amount")}
									minWidth='80'
									format={i18n.language === "no" ? "# ###' '" : "#,###"}
									width='120'
									maxWidth='300'
									textAlign='right'
								></ColumnDirective>
							</ColumnsDirective>
							{/* <AggregatesDirective>
								<AggregateDirective>
									<AggregateColumnsDirective>
										<AggregateColumnDirective field="Emission" type="Sum" footerTemplate={footerEmissionSum} />
									</AggregateColumnsDirective>
								</AggregateDirective>
							</AggregatesDirective> */}
							<Inject services={[Page, Toolbar, Edit, Filter, Group, Resize, Sort, Aggregate]} />
						</GridComponent>
					</div>
					{!isCompanyRegistered && <AddReportingEntityModal props={props} />}
				</div>
				{loader}
			</div>
		)
	}
}

export default Suppliers
