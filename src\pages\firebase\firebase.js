import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getStorage } from 'firebase/storage'

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_apiKey,
  authDomain: import.meta.env.VITE_FIREBASE_authDomain,
  projectId: import.meta.env.VITE_FIREBASE_projectId,
  storageBucket: import.meta.env.VITE_FIREBASE_storageBucket,
  messagingSenderId: import.meta.env.messagingSenderId,
  appId: import.meta.env.VITE_FIREBASE_appId,
  measurementId: import.meta.env.VITE_FIREBASE_measurementId
}

const app = initializeApp(firebaseConfig)

const storage = getStorage(app)

let dynamicStorage = (url) => getStorage(app, url || import.meta.env.VITE_FIREBASE_storageBucket)

const firebaseAuth = getAuth(app)

export { storage, dynamicStorage, app as default, firebaseAuth }
