import React from "react"

import PrimaryDataMetric from "../../components/ui/PrimaryDataMetric"
export default function Scope3CategorySection({ categories, t, scopeCategoryPrimaryData }) {
	const categoriesNames = [
		t("report:Cat1"),
		t("report:Cat2"),
		t("report:Cat3"),
		t("report:Cat4"),
		t("report:Cat5"),
		t("report:Cat6"),
		t("report:Cat7"),
		t("report:Cat8"),
	]

	return (
		<div className='grid grid-cols-2 '>
			{categories.map((category, index) => (
				<div className='print:break-inside-avoid-page' key={index}>
					<div className={`mt-6 border-2 border-c_border px-2 py-2 rounded-md ${index % 2 === 0 ? "mr-8" : ""}`}>
						<div className='text-md print:text-lg'>{categoriesNames[index]}</div>
						<div className='flex justify-between items-center'>
							<div className='text-md font-bold print:text-lg'>
								{parseFloat(category?.Emission || 0).toFixed(2)} tCo2e
								<PrimaryDataMetric value={scopeCategoryPrimaryData[index]?.Percentage || 0} t={t} />
							</div>
							<p className={`text-white text-sm bg-C${index + 1} !px-2 py-1 rounded-md`}>
								{parseFloat(category?.Percentage || 0).toFixed(2)} %
							</p>
						</div>
					</div>
				</div>
			))}
		</div>
	)
}
