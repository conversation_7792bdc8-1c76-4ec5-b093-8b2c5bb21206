const setRevenueOfCompany = async (payload) => {
	try {
		const data = await context.functions.execute("updateCompanyEmission", {
			...payload,
		})

		return data
	} catch (error) {
		throw error
	}
}

exports = async function (input) {
	try {
		const request = context.services.get("mongodb-atlas").db("co2-intensities-dev").collection("companies")

		const result = await request.find({ RegistrationNumber: input.RegistrationNumber }).toArray()

		const { year } = input

		if (input.setRevenue) {
			await setRevenueOfCompany({
				RegistrationNumber: input.RegistrationNumber,
				year: year,
				Revenue: input.Emission[year].Revenue,
			})
		}

		if (result && result.length) {
			const { Emissions } = result[0]

			let Emission = input.Emission

			let newEmissions = {
				...Emissions,
				...Emission,
			}

			const data = {
				Emissions: {
					...newEmissions,
				},
			}

			await request.updateOne({ RegistrationNumber: input.RegistrationNumber }, { $set: data })
		} else {
			const { Emission, CreatedAt, Nace, CompanyName, RegistrationNumber } = input

			await request.insertOne({
				Emissions: Emission,
				CreatedAt,
				Nace,
				CompanyName,
				RegistrationNumber,
				IndustryName: "TEst",
				Sector: "Roadworks",
				Category: "",
				Currency: "NOK",
				Naics: "",
				Sic: "",
			})
		}
		return { success: true, year }
	} catch (error) {
		console.log(error)
		return { success: false, error: error.message }
	}
}
