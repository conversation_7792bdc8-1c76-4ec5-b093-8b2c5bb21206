import { useQuery } from "@tanstack/react-query"

export default function useGetMonthlyChartData(app, input,enabled=true) {
	return useQuery({
		queryKey: ["monthlyChartData", input],
		queryFn: async () => {
			const { RegistrationNumber, year, AnalysisIDs } = input
			if (input.RegistrationNumber !== "") return await app.getMonthlyChartData(RegistrationNumber, year, AnalysisIDs)
		},

		enabled: (input !== undefined && enabled),
		refetchOnMount: true,
		retry: 5,
	})
}
