/* eslint-disable import/namespace */
import "./App.css"
import { <PERSON><PERSON><PERSON><PERSON>, SignedIn, SignedOut, RedirectToSignIn } from "@clerk/clerk-react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { Chart, ArcElement } from "chart.js"
import React from "react"
import { BrowserRouter, Routes, Route } from "react-router-dom"
//import "bootstrap/dist/css/bootstrap.min.css"

import RealmApolloProvider from "./graphql/realmApolloClient"
import { RealmAppProvider } from "./realm/RealmAppProvider"

import "react-toastify/dist/ReactToastify.css"

const DefaultLayout = React.lazy(() => import("./pages/shared/Layout"))

Chart.register(ArcElement)

function App() {
	const appId = import.meta.env.VITE_REALM_APP_ID
	const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY
	// Create a client
	const queryClient = new QueryClient({
		defaultOptions: {
			logger: {
				warn: console.warn,
				error: console.error,
			},
			queries: {
				retry: false,
				refetchOnWindowFocus: false,
				refetchOnMount: false,
			},
		},
	})
	return (
		<RealmAppProvider appId={appId}>
			<RealmApolloProvider>
				<QueryClientProvider client={queryClient}>
					<ClerkProvider publishableKey={publishableKey}>
						<BrowserRouter>
							<SignedIn>
								<Routes>
									<Route path='*' element={<DefaultLayout />} />
								</Routes>
							</SignedIn>
							<SignedOut>
								<RedirectToSignIn />
							</SignedOut>
						</BrowserRouter>
						<ReactQueryDevtools initialIsOpen={false} />
					</ClerkProvider>
				</QueryClientProvider>
			</RealmApolloProvider>
		</RealmAppProvider>
	)
}

export default App
