{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "plugin:import/recommended"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}, "import/resolver": {"node": {"extensions": [".js", ".jsx"]}}}, "plugins": ["react", "react-hooks", "jsx-a11y", "import"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}]}}