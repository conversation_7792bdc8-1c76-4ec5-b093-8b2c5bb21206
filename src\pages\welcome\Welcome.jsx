/* eslint-disable import/no-unresolved */
import { useUser } from "@clerk/clerk-react"
import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector, useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"

import "../../styles/welcome.css"
import AddCompanyModal from "../../components/AddCompanyModal.component"
import FileUpload from "../../components/ui/FileUpload"
import {
	fileUploaded,
	handleWorkingOnData,
	openSubscriptionModal,
	isCompanyRegistered,
} from "../../store/actions/UserAction"
import { store } from "../../store/configureStore"
import { firebaseAuth } from "../firebase/firebase"

import { useToast } from "@/hooks"
import { uploadFileToGCS } from "@/services/gcs-service"

function Welcome() {
	const { t } = useTranslation("welcome")
	// eslint-disable-next-line
	const [drag, setDrag] = useState(false)
	const [file, setFile] = useState(null)
	const [isFileUploading, setIsFileUploading] = useState(false)
	const [fileName, setFileName] = useState("")
	const [userEmail, setUserEmail] = useState(null)
	const [isEmailAvailable, setIsEmailAvailable] = useState(false)
	const [isFileSelected, setIsFileSelected] = useState(false)
	const dropRef = useRef(null)
	const selectRef = useRef(null)
	const navigate = useNavigate()
	const { user } = useUser()
	const dispatch = useDispatch()
	const [progress, setProgress] = useState(null)
	//const app = useRealmApp()
	const [isOpen, setIsOpen] = useState(false)
	const loginUser = useSelector((state) => state.user.currentOrganization)

	const Toast = useToast()
	//const companyIsRegistered = useSelector((state)=>state.company)

	// Handle file drag and drop.
	const handleDrag = (e) => {
		e.preventDefault()
		e.stopPropagation()
	}
	const handleDragIn = (e) => {
		e.preventDefault()
		e.stopPropagation()
		if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
			setDrag(true)
		}
	}
	const handleDrop = (e) => {
		// when user drops file set its file details in state
		e.preventDefault()
		e.stopPropagation()
		setDrag(false)
		if (e.dataTransfer.files && e.dataTransfer.files.length) {
			selectRef.current.files = e.dataTransfer.files
			if (!userEmail) {
				handleStoreValueChange()
			}
			setFile(e.dataTransfer.files[0])

			setIsFileSelected(true)
			setIsFileUploading(false)
			//setIsEmailAvailable(true)
		}
	}

	// to handle file data if user selects file instead of drag and drop.
	const onSelectFile = () => {
		if (selectRef?.current?.files && selectRef.current.files.length) {
			if (!userEmail) {
				handleStoreValueChange()
			}
			setFile(selectRef.current.files[0])
			setIsFileSelected(true)
			setIsFileUploading(false)
			setIsEmailAvailable(true)
		}
	}

	useEffect(() => {
		// const checkUserUPloadedFile = async () => {
		// 	const realmUser = await app.getRealmUserData(user.id)
		// 	if (realmUser && realmUser["saf-t_files"] && realmUser["saf-t_files"].length) {
		// 		// return navigate("/dashboard");
		// 	}
		// }
		// checkUserUPloadedFile()
		// this div refresh to drag and drop container. we add event listeners for drag and drop functionality
		let div = dropRef.current
		div.addEventListener("dragenter", handleDragIn)
		div.addEventListener("dragover", handleDrag)
		div.addEventListener("drop", handleDrop)
		const unsubscribe = store.subscribe(handleStoreValueChange)
		return () => {
			// eslint-disable-next-line
			let div = dropRef.current
			if (div) {
				div.removeEventListener("dragenter", handleDragIn)
				div.removeEventListener("dragover", handleDrag)
				div.removeEventListener("drop", handleDrop)
			}
			unsubscribe()
			firebaseAuth?.signOut()
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		// Upon getting file set it's filename to show it on page.
		if (file?.name) {
			setFileName(file.name)
		} else {
			setFileName("")
		}
	}, [file])

	const openFileUpload = () => {
		selectRef.current.click()
	}

	const validateFile = (file) => {
		const allowedTypes = ["text/xml", "application/zip", "application/x-zip-compressed"]
		if (!allowedTypes.includes(file.type)) {
			Toast("error", "Only XML and ZIP files are allowed")

			return false
		}

		return true
	}

	const uploadFile = async () => {
		if (!file) return

		if (!validateFile(file)) return

		//setUploading(true)
		//setProgress(0)

		try {
			// Upload file to Google Cloud Storage via backend proxy

			const foldername = user.id
			setIsFileUploading(true)
			setProgress(0)
			await uploadFileToGCS(file, foldername, (progress) => {
				setProgress(progress)
			})
			setIsFileUploading(false)

			Toast("success", `${file.name} has been uploaded to Scope321`)

			setFile(null)
			setFileName("")
			dispatch(fileUploaded(true))
			dispatch(handleWorkingOnData(true))
			dispatch(isCompanyRegistered(true))
			navigate("/dashboard")
		} catch (error) {
			console.error("Upload failed:", error)
			Toast("error", "There was an error uploading your file. Please try again.")
			setFile(null)
			setFileName("")
			setIsFileUploading(false)
			setProgress(0)
			if (selectRef?.current) {
				selectRef.current.value = ""
			}
		}
	}

	const handleStoreValueChange = () => {
		const stateData = store.getState()
		if (stateData.user) {
			if (!userEmail) {
				setUserEmail(stateData.user.user.email)
			}
		}
	}

	useEffect(() => {
		if (userEmail) {
			setIsEmailAvailable(true)
		} else {
			if (isEmailAvailable) {
				setIsEmailAvailable(false)
			}
		}
	}, [userEmail, isEmailAvailable])

	return (
		// <Layout active='Welcome'>
		<div>
			<AddCompanyModal isOpen={isOpen} setIsOpen={setIsOpen} />
			<div className='container mx-auto p-8'>
				<div className='descContainer my-3 py-2 px-3 border rounded-sm'>
					<div className='headerTitle mb-1 font-bold'>{t("UploadTitle")}</div>
					<div className='text-2xl font-semibold mb-1'>{t("UploadSubTitle")}</div>
					<div className='mb-1 text-2xl'>{t("UploadDesc")}</div>
					{!loginUser?.RegistrationNumber && (
						<div className='my-2 text-lg'>
							{t("UploadDescOr")}{" "}
							<span aria-hidden={true} className='text-sky-600 hover:cursor-pointer' onClick={() => setIsOpen(true)}>
								{t("CompanyDetails")}
							</span>
						</div>
					)}
				</div>

				{loginUser?.Subscription === 0 && (
					<div className='bg-sky-100 p-3 border-l-4 border-sky-600 rounded-lg mb-3'>
						<div className='flex items-center'>
							<h3 className='mb-0  text-base font-semibold'>{t("subscriptionStatus")}</h3>
							<span className='mb-0 ml-2 text-xs bg-sky-600 px-2 py-0.5 text-white rounded-xl'>{t("substatus")}</span>
						</div>

						<p className='text-xm'>{t("subs_m1")}</p>
						<p className='text-xm'>{t("subs_m2")}</p>
						<button
							onClick={() => {
								dispatch(openSubscriptionModal())
							}}
							className='bg-sky-600 mt-3 px-3 text-white rounded-sm py-2 font-bold text-xs'
						>
							{t("contactUs")}
						</button>
					</div>
				)}

				<FileUpload
					progress={progress}
					dropRef={dropRef}
					selectRef={selectRef}
					uploadFile={uploadFile}
					onSelectFile={onSelectFile}
					openFileUpload={openFileUpload}
					isFileSelected={isFileSelected}
					isDisable={isFileUploading || !isEmailAvailable || !isFileSelected}
					fileName={fileName}
					isFileUploading={isFileUploading}
					t={t}
				/>

				{/* <div className="fileUploadContainer" ref={dropRef}>
					<div className="fileUploadContain">
						<div className="grid justify-items-center fileUpload">
							<div className="mb-2 dropText">{t("DropHere")}</div>
							<div className="mb-2 fileInput">
								<input type="file" id="file" className="fileInputTag" ref={selectRef} onChange={onSelectFile} />
								<img
									src={import.meta.env.PUBLIC_URL + "/undraw_File_sync.svg"}
									className="uploadImg"
									alt=""
									onClick={openFileUpload}
								></img>
							</div>
							{isUploading ? (
								<div className="mb-3">{t("PreparingYourData")}</div>
							) : (
								<div className="mb-3">{fileName}</div>
							)}
							<div>
								<button
									className={"uploadBtn " + (isUploading || !isEmailAvailable || !isFileSelected ? "disabled" : "")}
									disabled={isUploading || !isEmailAvailable || !isFileSelected}
									onClick={uploadFile}
								>
									{t("Upload")}
								</button>
							</div>
						</div>
					</div>
				</div> */}
			</div>
		</div>
		// </Layout>
	)
}

export default Welcome
