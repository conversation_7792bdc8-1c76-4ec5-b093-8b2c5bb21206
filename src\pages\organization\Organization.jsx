/* eslint-disable no-unused-vars */
/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
import { useOrganizationList, useUser } from "@clerk/clerk-react"
import { useState, useEffect, useCallback, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"

import AddCompanyModal from "../../components/AddCompanyModal.component"
import Button from "../../components/ui/Button"
import TextInputEdit from "../../components/ui/TextInputEdit"
import { useToast } from "../../hooks"
import { useRealmApp } from "../../realm/RealmAppProvider"
import "../../styles/organization.css"
import {
	refetchOrg,
	UserAction,
	currentOrganizationAction,
	openSubscriptionModal,
} from "../../store/actions/UserAction"
import useFullPageLoader from "../loader/useFullPageLoader"

import Company from "./company/Company.component"
import Grouping from "./grouping/Grouping"
import LockData from "./publish/PublishData"
import Setting from "./setting/Setting.component"
import Subscription from "./subscription"

const Organization = (props) => {
	const currentOrganization = useSelector((state) => state.user.currentOrganization)
	const [organizationId, setOrganizationId] = useState()
	const [currentOrganizationMembership, setCurrentOrganizationMembership] = useState({})
	const loginUser = useSelector((state) => state.user.currentOrganization)
	const app = useRealmApp()
	const [isAdmin, setIsAdmin] = useState(false)
	const [orgList, setOrgList] = useState([])
	const { getOrganizationMemberships, getOrganization, createOrganization } = useOrganizationList()
	const [orgName, setOrgName] = useState("")
	const dispatch = useDispatch()
	const { user } = useUser()
	const [loader, showLoader, hideLoader] = useFullPageLoader()
	const Alert = useToast()
	const { t } = useTranslation(["organization", "common"])
	const [isOpen, setIsOpen] = useState(false)
	const scrollToTop = useRef(null)

	const onOrganizationChangeSetup = (organizationId) => {
		setOrganizationId(organizationId)

		const currentOrganizationMembershipOfUser = orgList.find(
			(membership) => membership.organization.id == organizationId
		)
		if (!currentOrganizationMembershipOfUser) {
			return null
		}
		setOrgName(currentOrganizationMembershipOfUser?.organization?.name)
		setIsAdmin(currentOrganizationMembershipOfUser?.role === "admin")
		setCurrentOrganizationMembership(currentOrganizationMembershipOfUser)
	}

	const changeOrganization = async (organizationId) => {
		const res = await app.getRealmUserByOrgId(organizationId)

		dispatch(
			currentOrganizationAction({
				CompanyName: res.CompanyName,
				RegistrationNumber: res.RegistrationNumber,
				company: res.companyInfo,
				email: res.email,
				name: res.name,
				netlifyID: res.netlifyID,
				organizationId: res.organizationId,
				Subscription: res.Subscription,
				organizationCreated: res.organizationId ? true : false,
			})
		)

		onOrganizationChangeSetup(organizationId)
	}

	const changeOrgName = async (value) => {
		try {
			if (!value.trim()) {
				return
			}
			const org = await getOrganization(organizationId)
			await org.update({ name: value.trim() })
			setOrgName(value)
			getOrgList()
			dispatch(refetchOrg(true))
		} catch (e) {
			// handle error
		}
	}

	const getOrgList = async () => {
		const { data } = await user.getOrganizationMemberships()
		setOrgList(data)
	}

	const createOrganizationManually = async () => {
		try {
			//if (!realmUser.organizationId && realmUser.Subscription === 1) {
			let organization = await createOrganization({ name: loginUser.CompanyName })
			let organizationId = organization.id
			showLoader()

			await app.updateUser({ organizationId: organizationId, netlifyID: user.id })

			dispatch(UserAction({ ...loginUser, organizationCreated: true }))
			dispatch(
				currentOrganizationAction({
					CompanyName: loginUser.CompanyName,
					RegistrationNumber: loginUser.RegistrationNumber,
					Subscription: 1,
					email: loginUser.email,
					company: loginUser.company,
					name: loginUser.name,
					netlifyID: loginUser.netlifyID,
					organizationId: organization.id,
					organizationCreated: true,
				})
			)
			dispatch(refetchOrg(true))
			hideLoader()
			Alert("success", t("organization_created_message"))
		} catch (error) {
			//handle error
		}
	}

	useEffect(() => {
		onOrganizationChangeSetup(currentOrganization.organizationId)
	}, [currentOrganization.organizationId, orgList])

	useEffect(() => {
		getOrgList()
	}, [currentOrganization.organizationId])

	const backToAccount = () => {
		props.navigate("/user/profile")
	}

	useEffect(() => {
		scrollToTop.current.scrollIntoView()
	}, [])

	return (
		<div className='flex flex-row justify-center' ref={scrollToTop}>
			<div className='md:container mx-auto px-5 pt-3'>
				{/* company information */}

				<AddCompanyModal isOpen={isOpen} setIsOpen={setIsOpen} />

				<Setting t={t} backToAccount={backToAccount} loginUser={loginUser} />

				<Company
					loginUser={loginUser}
					// backToAccount={backToAccount}
					t={t}
					openAddCompanyModal={() => setIsOpen(true)}
				/>

				<LockData t={t} />

				<Subscription loginUser={loginUser} t={t} />

				<Grouping t={t} />

				<div className='cl-component cl-user-profile'>
					<div className='cl-main mt-4'>
						<div className='cl-user-profile-card cl-themed-card'>
							<div className='cl-titled-card-list'>
								<div className='text-2xl font-extrabold'>{t("organization")}</div>

								{loginUser?.Subscription === 1 && !loginUser?.organizationId && (
									<>
										<p className=''>{t("org_not_enable")}</p>
										<div className='w-full'>
											<div className='flex justify-end'>
												<Button title={t("enable_org_feature")} handleClick={() => createOrganizationManually()} />
											</div>
										</div>
									</>
								)}

								{loginUser?.Subscription === 1 && loginUser?.organizationId && (
									<>
										<div className='flex mt-4'>
											<div className='w-3/5'>
												{currentOrganizationMembership && currentOrganizationMembership.length != 0 ? (
													<>
														{isAdmin ? (
															<TextInputEdit
																value={orgName || "organization name"}
																showLabel={false}
																handleChange={(value) => {
																	changeOrgName(value)
																}}
															/>
														) : (
															<select
																className='w-100 custom-select'
																value={organizationId}
																onChange={(e) => changeOrganization(e.target.value)}
															>
																<option key='' value='' disabled>
																	--select--
																</option>
																{orgList.map((m) => (
																	<option key={m.id} value={m.organization.id}>
																		{m.organization.name}
																	</option>
																))}
															</select>
														)}
													</>
												) : (
													<></>
												)}
												{/* <select
														className="w-100 custom-select"
														value={organizationId}
														onChange={(e) => changeOrganization(e.target.value)}
													>
														<option key="" value="" disabled>
															--select--
														</option>
														{orgList.map((m) => (
															<option key={m.id} value={m.organization.id}>
																{m.organization.name}
															</option>
														))}
													</select> */}
												{/* {currentOrganizationMembership && currentOrganizationMembership.length != 0 ? (
														<TextInputEdit
															value="Name"
															showLabel={false}
															handleChange={(value) => {
																(value)
															}}
															disable={currentOrganizationMembership?.role === "admin"}
														/>
													) : (
														<></>
													)} */}
											</div>
											<div className='my-auto ml-10'>
												<b>Role : </b>
												{currentOrganizationMembership && currentOrganizationMembership.length != 0 ? (
													currentOrganizationMembership.role === "admin" ? (
														<span className='ml-3 mt-0 bg-sky-600 px-3 py-0.5 text-xs text-white rounded-xl'>
															{t("Administrator")}
														</span>
													) : (
														<span className='ml-3 mt-0 bg-sky-600 px-3 py-0.5 text-xs text-white rounded-xl'>
															{t("Member")}
														</span>
													)
												) : (
													<></>
												)}
											</div>
										</div>
										{/* <div className="w-full mt-5">
												<div className="flex justify-end">
													<Button title={t("common:Edit")} variation="secondary" />
													<Button title={t("common:Save")} />
												</div>
											</div> */}
									</>
								)}

								{loginUser?.Subscription === 0 && (
									<>
										<p className='mb-0'>{t("org_feat_with_multi_users")}</p>
										<div className='w-full'>
											<div className='flex justify-end mt-2'>
												<Button
													title={t("common:SubscriptionInquiry")}
													handleClick={() => dispatch(openSubscriptionModal())}
												/>
											</div>
										</div>
									</>
								)}
							</div>
						</div>
					</div>
				</div>

				<div className='mb-5'>
					<div className='cl-component cl-user-profile'>
						<div className='cl-main mt-4'>
							<div className='cl-user-profile-card cl-themed-card'>
								<div className='text-2xl font-extrabold'>Team</div>
								{loginUser.organizationId ? (
									<div className='cl-titled-card-list'>
										<div className='mx-3 mt-3'>
											{isAdmin && currentOrganizationMembership && currentOrganizationMembership?.organization && (
												<Invitations
													organization={currentOrganizationMembership?.organization}
													isAdmin={isAdmin}
													Alert={Alert}
													t={t}
												/>
											)}
										</div>
										<div className='mx-3 mt-3'>
											{currentOrganizationMembership && currentOrganizationMembership?.organization && (
												<Memberships
													organization={currentOrganizationMembership.organization}
													isAdmin={isAdmin}
													t={t}
													loggedUserId={user.id}
												/>
											)}
										</div>
									</div>
								) : (
									<p>{t("team_is_available_for_org")}</p>
								)}
							</div>
						</div>
					</div>
				</div>
				{loader}
			</div>
		</div>
	)
}

function Invitations({ organization, isAdmin, t, Alert }) {
	const [invitations, setInvitations] = useState([])
	const [emailAddress, setEmailAddress] = useState("")

	const getInvitations = organization.getInvitations

	const fetchInvitations = useCallback(async () => {
		try {
			const { data } = await getInvitations()
			const inviations = data.filter((invite) => invite.status !== "revoked" && invite.status !== "accepted")
			setInvitations(inviations)
		} catch (err) {
			console.error(err)
		}
	}, [getInvitations])

	useEffect(() => {
		fetchInvitations()
	}, [fetchInvitations])

	async function invite(e) {
		e.preventDefault()
		try {
			const { maxAllowedMemberships, membersCount, pendingInvitationsCount } = organization

			if (membersCount + pendingInvitationsCount >= maxAllowedMemberships) {
				Alert("error", t("max_members_reached_message"))
				return
			}

			await organization.inviteMember({
				emailAddress,
				role: "basic_member",
			})
			Alert("success", t("invitation_sent_message"))
			setEmailAddress("")
			fetchInvitations()
		} catch (err) {
			console.error(err)
		}
	}

	async function revoke(invitation) {
		try {
			await invitation.revoke()
			Alert("success", t("revoked_successfully_message"))
			fetchInvitations()
		} catch (err) {
			console.error(err)
		}
	}

	return (
		<>
			<div className='flex flex-col'>
				<div>
					<b className='text-lg'>{t("InviteMembers")}</b>
					<div className='my-1'>
						<div className='flex justify-between'>
							<input
								className='px-3 mr-2 w-96 border rounded'
								type='email'
								name='email_address'
								placeholder='✉ <EMAIL>'
								value={emailAddress}
								onChange={(e) => setEmailAddress(e.target.value)}
							/>
							<Button handleClick={invite} title={`✉ ${t("SendInvite")}`} />
						</div>
					</div>
					<small>{t("users_will_be_invited")} </small>
				</div>
			</div>
			<div className='pt-3'>
				<b>{t("PendingInvites")}</b>
				<ul>
					{invitations.map((invitation) => (
						<li key={invitation.id} className='flex justify-between items-center mt-2 mb-2'>
							<div className='w-32'>
								<span>{invitation.emailAddress}</span>
							</div>
							<div>
								<span className='label mx-2'>{t("BasicMember")}</span>
							</div>
							<div>
								<Button
									title={t("RevokeInvitation")}
									variation='danger'
									handleClick={(e) => {
										e.preventDefault()
										revoke(invitation)
									}}
								/>
							</div>
						</li>
					))}
				</ul>
				<hr />
			</div>
		</>
	)
}

function Memberships({ organization, isAdmin = false, loggedUserId, t }) {
	const [memberships, setMemberships] = useState([])

	const getMemberships = organization.getMemberships

	const fetchMemberships = useCallback(async () => {
		try {
			const { data } = await getMemberships()
			setMemberships(data)
		} catch (err) {
			console.error(err)
		}
	}, [getMemberships])

	useEffect(() => {
		fetchMemberships()
	}, [fetchMemberships])

	async function remove(userId) {
		try {
			if (userId === loggedUserId) {
				return
			}
			await organization.removeMember(userId)
			fetchMemberships()
		} catch (err) {
			console.error(err)
		}
	}

	async function switchRole(membership) {
		if (membership.publicUserData.userId === loggedUserId) {
			return
		}
		const role = membership.role === "admin" ? "basic_member" : "admin"
		try {
			await membership.update({ role })
			fetchMemberships()
		} catch (err) {
			console.error(err)
		}
	}

	return (
		<div>
			<b>Members</b>
			<ul className='pl-2'>
				{memberships.map((membership) => {
					return (
						<li key={membership.id} className='flex flex-wrap justify-between align-middle mt-2'>
							<div className='flex w-80'>
								<span className='member-img'>{membership.publicUserData.identifier[0].toUpperCase()}</span>
								<span className='my-auto'>{membership.publicUserData.identifier}</span>
							</div>

							<div className='mx-2 my-auto'>
								{membership.role === "admin" ? (
									<span className='label'>{t("Administrator")}</span>
								) : (
									<span className='label'>{t("BasicMember")}</span>
								)}
							</div>
							{isAdmin ? (
								<div className='w-80'>
									<div className='my-auto'>
										{!isAdmin || membership.role === "admin" ? (
											<></>
										) : (
											<div className='flex justify-between'>
												<Button
													title={t("SwitchRole")}
													variation='secondary'
													handleClick={(e) => {
														e.preventDefault()
														switchRole(membership)
													}}
												/>

												<Button
													title={t("DeleteMember")}
													variation='danger'
													handleClick={(e) => {
														e.preventDefault()
														remove(membership.publicUserData.userId)
													}}
												/>
											</div>
										)}
									</div>
								</div>
							) : (
								<div></div>
							)}
						</li>
					)
				})}
			</ul>
		</div>
	)
}

export default Organization
