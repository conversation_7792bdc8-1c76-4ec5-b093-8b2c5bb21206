/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { FiX } from "react-icons/fi"
import Select from "react-select"

//import Button from "../../components/ui/Button"
import Spinner from "../../components/ui/Spinner"
import useGetCompanyAnalysis from "../../hooks/transactions/useGetCompanyAnalysis"
import { useRealmApp } from "../../realm/RealmAppProvider"

// import { useTranslation } from 'react-i18next'

const Chip = ({ projects, handleClick, color }) => {
	return (
		<div className='w-full flex flex-row flex-wrap items-center mt-4'>
			{projects.map((project) => {
				return (
					<div key={project.value} className={`flex items-center w-fit bg-${color} ml-1 rounded-full px-2 py-1 mb-1`}>
						<p className='text-white mr-2 text-xs m-0 p-0'>{project.label}</p>
						<FiX onClick={() => handleClick(project.value)} className='cursor-pointer text-white text-xs ' />
					</div>
				)
			})}
		</div>
	)
}

export default function AddCompanyModal({
	isOpen,
	setIsOpen,
	RegistrationNumber,
	handleFilters,
	setProjects,
	setDepartments,
	projects,
	departments,
}) {
	const { t } = useTranslation(["transactions"])
	//const [projects, setProjects] = useState([])
	//const [departments, setDepartments] = useState([])
	const app = useRealmApp()
	const Analysis = useGetCompanyAnalysis(app, RegistrationNumber)
	const [projectOptions, setProjectOptions] = useState([])
	const [departmentOptions, setDepartmentOptions] = useState([])

	useEffect(() => {
		if (Analysis.data && Analysis.data.success) {
			setProjectOptions(Analysis.data.projects)
			setDepartmentOptions(Analysis.data.departments)
		} else {
			setProjectOptions([])
			setDepartmentOptions([])
		}
	}, [Analysis.data])

	// function that will handle deletion of projects from projects array which is
	// array of object each object contain value and label where value is project id
	const handleDeleteProjects = (projectId) => {
		try {
			// filter out the project from projects array which is not equal to projectId
			let project = projects.filter((project) => project.value !== projectId)
			setProjects(project)
			handleFilters([...departments, ...project])
		} catch (error) {
			//
		}
	}

	const handleDeleteDepartments = (departmentId) => {
		try {
			// filter out the project from projects array which is not equal to projectId
			let department = departments.filter((department) => department.value !== departmentId)
			setDepartments([...department])
			handleFilters([...projects, ...department])
		} catch (error) {
			//
		}
	}

	// const applyFilter = () => {
	// 	handleFilters([...departments, ...projects])
	// }

	// const clearFilters = () => {
	// 	setProjects([])
	// 	setDepartments([])
	// 	handleFilters([])
	// }

	return (
		<div
			className={
				"fixed z-10 bg-black bg-opacity-50 inset-0 transform ease-in-out " +
				(isOpen
					? "transition-opacity opacity-100 duration-500 h-full translate-x-0 z-[999] overflow-y-auto"
					: "transition-all delay-500 opacity-0 translate-x-full")
			}
		>
			<div
				className={
					"p-8 w-[450px] right-0 absolute bg-white h-min min-h-full shadow-xl delay-400 duration-500 ease-in-out transition-all transform " +
					(isOpen ? "translate-x-0" : "translate-x-full")
				}
			>
				{/* <FaTimes color="red" size={30} /> */}
				{Analysis.isLoading ? (
					<div className='mt-[50%]'>
						<Spinner />
					</div>
				) : (
					<>
						<div className='flex justify-between items-center'>
							<h3 className='text-lg font-semibold'>{t("apply_filter")}</h3>
							<FiX
								onClick={() => {
									setIsOpen(false)
								}}
								size={30}
								className='text-rose-800 cursor-pointer -mt-6'
							/>
						</div>

						<label htmlFor='userName' className='font-bold block text-slate-800 mt-6'>
							{t("projects")}
						</label>
						<Select
							closeMenuOnSelect={true}
							onChange={(e) => {
								if (e && e.value) {
									let newProjects = [...projects, e]
									setProjects(newProjects)
									handleFilters([...departments, ...newProjects])
								}
							}}
							options={projectOptions}
						/>

						{projects && projects.length > 0 && (
							<Chip projects={projects} color='sky-600' handleClick={handleDeleteProjects} />
						)}

						<label htmlFor='userName' className='font-bold block text-slate-800 mt-6'>
							{t("departments")}
						</label>
						<Select
							closeMenuOnSelect={true}
							onChange={(e) => {
								if (e && e.value) {
									let newDepartments = [...departments, e]
									setDepartments(newDepartments)
									handleFilters([...newDepartments, ...projects])
								}
							}}
							options={departmentOptions}
						/>

						{departments && departments.length > 0 && (
							<Chip projects={departments} color='amber-600' handleClick={handleDeleteDepartments} />
						)}

						{/* <div className='flex flex-row justify-between mt-5'>
							<Button
								title={t("apply_filter")}
								variation='secondary'
								handleClick={() => {
									applyFilter()
								}}
								width={"45%"}
							/>
							<Button
								title={t("clear_filter")}
								variation='danger'
								handleClick={() => {
									clearFilters()
									//setIsOpen(false)
								}}
								width={"45%"}
							/>
						</div>

						<span className='mt-5 text-red-500'>NOTE: Please press apply filter button to see effect</span> */}
					</>
				)}
			</div>
		</div>
	)
}
