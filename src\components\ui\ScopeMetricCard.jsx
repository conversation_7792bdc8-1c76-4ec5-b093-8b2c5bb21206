import { Card, Flex, Text } from "@tremor/react"
import React from "react"

import <PERSON><PERSON><PERSON> from "./PieChart"

export default function ScopeMetricCard({
	title = "",
	colors = ["amber", "slate"],
	value = 0,
	total = 0,
	showValue = true,
	language = "en-EN",
}) {
	return (
		<Card
			decoration='left'
			decorationColor={"none"}
			className='shadow-lg hover:shadow-xl transition-shadow duration-300 py-3'
		>
			<Flex justifyContent='between'>
				<Flex flexDirection='col'>
					<Text className='text-slate-600 align-top self-baseline font-bold text-lg'>{title}</Text>
					{showValue && (
						<Flex justifyContent='start' alignItems='baseline' className='space-x-2 truncate'>
							<div className='text-2xl font-semibold'>
								{Intl.NumberFormat(language, {
									maximumFractionDigits: 2,
								}).format(value)}
							</div>
							<span className='truncate'>
								<Text>tCo2e</Text>
							</span>
						</Flex>
					)}

					{/* {showPrimaryMatric && (
						<Flex justifyContent='start' alignItems='baseline' className='space-x-2 truncate'>
							<div className={`text-md font-bold mx-0 text-${getPrimaryDataColors(primaryDataPercentage)}-500`}>
								{Intl.NumberFormat(language, {
									maximumFractionDigits: 2,
								}).format(primaryDataPercentage)}{" "}
								%
							</div>
							<span className='truncate'>
								<Text>{t("common:primary")}</Text>
							</span>
						</Flex>
					)} */}
				</Flex>

				<PieChart
					className='h-28 w-28'
					showLengend={false}
					variant='donut'
					total={total}
					colors={colors}
					data={value}
					language={language}
				/>
			</Flex>
		</Card>
	)
}
