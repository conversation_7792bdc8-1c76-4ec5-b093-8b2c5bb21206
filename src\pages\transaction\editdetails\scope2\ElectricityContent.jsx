/* eslint-disable import/no-unresolved */
import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { FACTOR_LIST_ELECTRICITY } from "./constants/electricity"
import { co2_mix, DEFAULT_LIST_ELECTRICITY } from "./constants/electricTransportation"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function ElectricityContent(props) {
	const { electricityRows, PeriodYear, period, updateElectricityEmission } = props
	const unitInputRef = useRef()
	const { t } = useTranslation("transactions")
	const Toast = useToast()

	const METRIC_LIST_ELECTRICITY = [
		{
			label: t("Strøm med opprinnelsesgaranti"),
			value: "WCO",
			factor: "og",
			unit: "kwh",
			type: 1,
		},
		{
			label: t("Norsk residual mix(uten OG)"),
			value: "UOG",
			factor: "residual",
			unit: "kwh",
			type: 2,
		},
		{ label: t("European Mix"), value: "WOCO", factor: "euMix", unit: "kwh", type: 3 },
		{ label: t("Other"), value: "Oth", factor: "og", unit: "kwh", type: 4 },
	]

	const [metric, setMetric] = useState(METRIC_LIST_ELECTRICITY[0])

	const [factor, setFactor] = useState(0.0)

	const [unit, setUnit] = useState(0.0)

	const [label, setLabel] = useState(METRIC_LIST_ELECTRICITY[0].label)

	const addElectricityRow = (
		kwh,
		scope2,
		scope3,
		locationBased,
		consumptionBased,
		marketBased,
		nuclearCalculated,
		renewable_energy,
		non_renewable_energy
	) => {
		const newElectricityRow = {
			metric: label,
			factor: Number(factor),
			unit: Number(unit),
			scope1: 0,
			scope2: Number(scope2),
			scope3: Number(scope3),
			kwh: Number(kwh),
			locationBased: Number(locationBased),
			consumptionBased: Number(consumptionBased),
			marketBased: Number(marketBased),
			nuclear: Number(nuclearCalculated),
			renewable_energy: Number(renewable_energy),
			non_renewable_energy: Number(non_renewable_energy),
		}

		const updatedElectricityRows = [...electricityRows, newElectricityRow]

		updateElectricityEmission(newElectricityRow, false, updatedElectricityRows)
	}

	const calculateElectricityEmission = () => {
		let KWH = 0.0
		let locationBased = 0
		let consumptionBased = 0
		let marketBased = 0
		let nuclearCalculated = 0
		const co2Mix = co2_mix[PeriodYear]
		const nuclearFactor = co2Mix["NO"]?.nuclear || 0

		let type = metric.type

		let renewable_energy = 0

		let non_renewable_energy = 0

		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0 && parseInt(period) <= 0) {
			Toast("error", t("electricity_error"))
			return
		}

		let scope2Value = (parseFloat(unit) * parseFloat(factor)) / 1000
		let scope3Value = parseFloat(unit) * (DEFAULT_LIST_ELECTRICITY.wttTD + DEFAULT_LIST_ELECTRICITY.wttG)
		KWH = parseFloat(unit)

		locationBased = (KWH * co2Mix?.["NO"]?.co2) / 1000
		marketBased = parseFloat(scope2Value) / 1000
		consumptionBased = (KWH * co2Mix?.["NO"]?.consumption) / 1000

		if (type === 2) {
			nuclearCalculated = nuclearFactor * parseFloat(unit)
		}

		switch (type) {
			case 1:
				renewable_energy = KWH * 1
				non_renewable_energy = non_renewable_energy + non_renewable_energy * 0
				break
			case 2:
				renewable_energy = KWH * co2_mix[PeriodYear]["NO"]["renewables"]
				non_renewable_energy = KWH * co2_mix[PeriodYear]["NO"]["fossil"]

				// add neuclear calculate its percentage on base of units
				break
			case 3:
				renewable_energy = KWH * co2_mix[PeriodYear]["SE"]["renewables"]
				non_renewable_energy = KWH * co2_mix[PeriodYear]["SE"]["fossil"]
				break
			case 4:
				renewable_energy = KWH * co2_mix[PeriodYear]["NO"]["renewables"]
				non_renewable_energy = KWH * co2_mix[PeriodYear]["NO"]["fossil"]
				break
			default:
				break
		}

		addElectricityRow(
			KWH,
			scope2Value,
			scope3Value,
			locationBased,
			consumptionBased,
			marketBased,
			nuclearCalculated,
			renewable_energy,
			non_renewable_energy
		)
	}

	const metricChange = (e) => {
		setMetric(e)
		setLabel(e.label)
		setFactor(parseFloat((FACTOR_LIST_ELECTRICITY[e.factor][PeriodYear] * 1000).toFixed(3)))
		unitInputRef.current.focus()
	}

	const deleteElectricityRow = (index) => {
		let electricityRow = electricityRows[index]
		let newElectricityRows = [...electricityRows]
		newElectricityRows.splice(index, 1)

		updateElectricityEmission(electricityRow, true, newElectricityRows)
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{electricityRows.map((electricity, index) => (
				<div key={index}>
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{t(electricity.metric)}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{electricity.factor}</span>
								<span className='custom-span-unit-value-save'>
									{t("Gram")} {t("Co2e")}
								</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{electricity.unit}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kwh")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(electricity.scope1 + electricity.scope2 + electricity.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteElectricityRow(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}
			<div className='grid grid-cols-4 gap-10 my-2'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"metric-electricity-"}>
						{t("Metric")}
					</label>
					<Select
						id={"metric-electricity-"}
						value={{ ...metric, label: t(metric.label) }}
						onChange={metricChange}
						options={METRIC_LIST_ELECTRICITY}
						maxMenuHeight={150}
					/>
				</div>
				<div>
					<Input
						type='number'
						label={t("Factor")}
						labelColor='text-sky-500'
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("Co2e")}
					/>

					{/* <label className="text-slate-800 font-semibold whitespace-nowrap" htmlFor={"factor-electricity-" + index}>
                            {t("Factor")}
                        </label>
                        <div className="d-flex position-relative">
                            <input
                                type="number"
                                value={electricity.factor}
                                id={"factor-electricity-" + index}
                                step={0.01}
                                onChange={metricFactor}
                                className="form-control unit-gram-co2e"
                                aria-describedby="factor"
                                onFocus={handleFocus}
                            ></input>
                            <span className="custom-span-unit-value-gram-co2-electricity">
                                {t("Gram")} <br /> {t("Co2e")}
                            </span>
                        </div> */}
				</div>
				<div>
					<Input
						type='number'
						label={t("Units")}
						labelColor='text-sky-500'
						ref={unitInputRef}
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						unit={t("kwh")}
					/>
				</div>

				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateElectricityEmission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default ElectricityContent
