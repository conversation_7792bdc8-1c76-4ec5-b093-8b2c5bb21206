{"id": "6203c3988ed59118965d6fbb", "name": "insertSupplier", "type": "DATABASE", "config": {"operation_types": ["INSERT"], "database": "test-scope321", "collection": "company", "service_name": "mongodb-atlas", "match": {}, "project": {}, "full_document": true, "full_document_before_change": false, "unordered": true, "skip_catchup_events": false, "tolerate_resume_errors": false}, "disabled": false, "event_processors": {"FUNCTION": {"config": {"function_name": "insertSupplierFunction"}}}}