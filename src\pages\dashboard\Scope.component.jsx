import { Card, Flex, Text, Metric } from "@tremor/react"
import React from "react"
import Select from "react-select"

import <PERSON><PERSON><PERSON> from "../../components/ui/PieChart"

export default function Scope({
	heading = "",
	colors = [],
	decorationColor = "rose",
	value = 0,
	primaryData = [],
	primaryDataLabel = "",
	total = 0,
	language = "no-NO",
	primaryColor = "rose",
	select = false,
	onChangeHandler,
	dropDownWidth = null,
	selectedValue = "",
	downStreamEmission = null,
	options = [],
}) {
	const dropDownStyles = {
		width: dropDownWidth ? `${dropDownWidth}px` : "100%",
	}

	return (
		<div className="w-full">
			<Flex flexDirection="col" className="gap-3">
				<Card key={1} decoration="left" decorationColor={decorationColor} className="gap-3 py-3">
					<Flex className="w-full">
						<Flex flexDirection="col" className="gap-2">
							<Flex alignItems="start">
								<Text className="font-bold">{heading}</Text>
							</Flex>
							{select && (
								<Flex alignItems="start" className="print:hidden">
									<Select
										styles={dropDownStyles}
										id={"testing786"}
										value={selectedValue}
										onChange={(e) => {
											onChangeHandler(e)
										}}
										options={options}
									/>
								</Flex>
							)}

							<Flex flexDirection="row" justifyContent="start">
								<div>
									<Flex justifyContent="start" alignItems="baseline" className="space-x-2 truncate">
										<Metric>
											{Intl.NumberFormat(language, {
												maximumFractionDigits: 2,
											}).format(value)}
										</Metric>
										<Text className="truncate">tCo2e</Text>
									</Flex>
									<Flex justifyContent="start" alignItems="baseline" className="space-x-1 truncate">
										<p className={`text-md font-bold text-${primaryColor}-500`}>
											{Intl.NumberFormat(language, {
												maximumFractionDigits: 2,
											}).format(primaryData)}
										</p>
										<Text className="truncate">% {primaryDataLabel}</Text>
									</Flex>
								</div>
								{downStreamEmission && (
									<div className="ml-10">
										<Flex justifyContent="start" alignItems="baseline" className="space-x-1 truncate">
											<Metric>
												{Intl.NumberFormat(language, {
													maximumFractionDigits: 2,
												}).format(downStreamEmission)}
											</Metric>
											<Text className="truncate">tCo2e</Text>
										</Flex>
										<Flex justifyContent="start" alignItems="baseline" className="space-x-1 truncate">
											<Text className="truncate">{"Including dwonstream"}</Text>
										</Flex>
									</div>
								)}
							</Flex>
						</Flex>

						<PieChart
							className="h-28 w-28"
							showLengend={false}
							variant="donut"
							total={total}
							colors={colors}
							data={value}
							language={language}
						/>
					</Flex>
				</Card>
			</Flex>
		</div>
	)
}
