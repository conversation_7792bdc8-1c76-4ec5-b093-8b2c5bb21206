import { InPlaceEditorComponent } from "@syncfusion/ej2-react-inplace-editor"
import React from "react"
import "@/styles/TextInputEdit.css"

export default function TextInputEdit({
	value = "Scope321",
	label = "Company name",
	handleChange,
	disable = false,
	showLabel = true,
}) {
	return (
		<div className="flex flex-col mt-1">
			{showLabel && <p className="mb-1 text-sm font-bold text-labelColor">{label}</p>}
			<InPlaceEditorComponent
				id="element"
				actionSuccess={(e) => {
					handleChange(e.value)
				}}
				disabled={disable}
				type="Text"
				data-underline={false}
				mode="Inline"
				value={value}
			/>
		</div>
	)
}
