/* eslint-disable import/no-unresolved */
import { Flex } from "@tremor/react"
import React, { useState, useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"

import RichTextEditor from "../../components/RichTextEditor"
import { Badge } from "../../components/ui/badge"
import DonutChart from "../../components/ui/DonutChart"
import PrimaryDataMetric from "../../components/ui/PrimaryDataMetric"
import ScopeMetricCard from "../../components/ui/ScopeMetricCard"
import Spinner from "../../components/ui/Spinner"
import {
	useGetScopeChartData,
	useGetScopeCategoryData,
	useGetCO2AddedData,
	useGetMonthlyChartData,
	useGetYearlyChartData,
} from "../../hooks"
import useGetCompanyAnalysis from "../../hooks/transactions/useGetCompanyAnalysis"
import useGetProjectEmission from "../../hooks/transactions/useGetProjectEmission"
import { useRealmApp } from "../../realm/RealmAppProvider"
import "../../styles/dashboard.css"
import "../../styles/sidebar.css"
import "./index.css"
import { getPrimaryDataColors } from "../../services/helper"
import BarChartComponent from "../dashboard/BarChart.component"
import Scope from "../dashboard/Scope.component"
import useFullPageLoader from "../loader/useFullPageLoader"

import { data } from "./constants"
import Loader from "./Loader"
import Scope3CategorySection from "./Scope3CategorySection"
import TableComponent from "./TableComponent"

import AddOnAccessModal from "@/components/AddOnAccessModal.component"

export default function ProjectReport() {
	const currentOrganization = useSelector((state) => state.user.currentOrganization)
	const reportingYear = useSelector((state) => state.reporting)
	const hasProjectAccess = useSelector((state) => state.ProjectReport)
	const app = useRealmApp()
	const { t, i18n } = useTranslation(["dashboard", "report", "common"])

	const [scopeCategoryData, setScopeCategoryData] = useState([])

	const [projectName, setProjectName] = useState("")

	const [loader, showLoader, hideLoader] = useFullPageLoader()

	const [projects, setProjects] = useState([])

	const [scope3EmissionIncludingDownStream, setScope3EmissionIncludingDownStream] = useState(null)

	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""

	const scrollToTop = useRef(null)

	const isCompanyRegistered = useSelector((state) => state.company)

	const [currentProject, setCurrentProject] = useState("")

	const [queryEnabled, setQueryEnabled] = useState(false)

	const [scopeDataLocationBased, setScopeDataLocationBased] = useState({
		Scope_1: 0.0,
		Scope_2: 0.0,
		Scope_3: 0.0,
		total: 0.0,
		isDefault: true,
	})

	const [scope2CalculationMethod, setScope2CalculationMethod] = useState({
		name: "marketBased",
		label: t("market-based"),
		value: "market-based",
	})

	const scope2Methods = [
		{ name: "combustionBased", label: t("consumption-based"), value: "consumption-based" },
		{ name: "locationBased", label: t("location-based"), value: "location-based" },
		{ name: "marketBased", label: t("market-based"), value: "market-based" },
	]

	const [isTotalCo2Added, setIsTotalCo2Added] = useState(false)

	const [totalEnergy, setTotalEnergy] = useState(0)

	const [energyData, setEnergyData] = useState([])

	const [scopeCategoryPrimaryData, setScopeCategoryPrimaryData] = useState([])

	const [scopePrimaryData, setScopePrimaryData] = useState([])

	const [totalPrimaryScope, setTotalPrimaryScope] = useState(null)

	const [downStreamCatagoryEmission, setDownStreamCatagoryEmission] = useState(null)

	const [projectReportData, setProjectReportData] = useState({ ...data })

	const [isReportDataFetched, setIsReportDataFetched] = useState(false)

	const [path, setPath] = useState("")

	const skelton = useRef(null)

	const [scopeData, setScopeData] = useState({
		Scope_1: 0.0,
		Scope_2: 0.0,
		Scope_3: 0.0,
		total: 0.0,
		isDefault: true,
	})

	const [scope2Emission, setScope2Emission] = useState(0)

	const [companyInfoEdit, setCompanyInfoEdit] = useState({
		scope1emission: false,
		scope2emission: false,
		scope3emission: false,

		Metodikk: false,
		refer: false,
	})

	const [scope2Data, setScope2Data] = useState([])

	const [energyChartValues, setEnergyChartValues] = useState([])

	const [projectAnalysisEmissions, setProjectAnalysisEmissions] = useState([])

	const [departmentAnalysisEmissions, setDepartmentAnalysisEmissions] = useState([])

	const [isAnalysisEmissionAdded, setIsAnalysisEmissionAdded] = useState(false)

	const [emissions, setEmissions] = useState([])

	const [yearlyEmissionData, setYearlyEmissionData] = useState([])

	const [departments, setDepartments] = useState([])

	const scope_Data = useGetScopeChartData(
		app,
		{
			RegistrationNumber,
			year: reportingYear,
			AnalysisIDs: [currentProject],
		},
		queryEnabled
	)

	const monthlyChartData = useGetMonthlyChartData(
		app,
		{
			RegistrationNumber,
			year: reportingYear,
			AnalysisIDs: [currentProject],
		},
		queryEnabled
	)

	const scope_catagory_data = useGetScopeCategoryData(
		app,
		{
			RegistrationNumber,
			year: reportingYear,
			AnalysisIDs: [currentProject],
		},
		queryEnabled
	)

	const CO2AddedData = useGetCO2AddedData(
		app,
		{
			RegistrationNumber,
			year: reportingYear,
			AnalysisIDs: [currentProject],
		},
		queryEnabled
	)

	const Analysis = useGetCompanyAnalysis(app, RegistrationNumber)
	const projectEmission = useGetProjectEmission(app, RegistrationNumber, reportingYear)

	const yearlyData = useGetYearlyChartData(app, { RegistrationNumber, AnalysisIDs: [currentProject] })

	const calculateEmission = (e) => {
		let { name } = e
		let s = { ...scopeData }
		s.total -= s.Scope_2
		let total = s.total + Number(scope2Data[name])
		s = { ...scopeData, Scope_2: Number(scope2Data[name]), total: Number(total) }
		setScope2CalculationMethod(e)
		setScopeData(s)
	}

	const handleOpenEditor = (key) => {
		let newData = { ...companyInfoEdit }
		newData[key] = !newData[key]
		setCompanyInfoEdit(newData)
	}

	const handleImageUploadSuccess = (args) => {
		let s = JSON.parse(args.e.target.response)

		let path1 = `${args.e.target.responseURL}?alt=media&token=${s.downloadTokens}`

		let filename = document.querySelectorAll(".e-file-name")[0]
		filename.innerHTML = args.file.name.replace(document.querySelectorAll(".e-file-type")[0].innerHTML, "")
		filename.title = path1

		setPath(path1)
		//setPath("");
		//}
	}

	const handleChangeContent = (key, content) => {
		let newData = { ...projectReportData }
		newData[key] = content
		setProjectReportData(() => newData)
	}

	const handleClickOnLink = (event) => {
		const target = event.target

		// Check if the clicked element has the data attributes (i.e., a valid clickable cell)
		if (target.matches("td[data-id]")) {
			const id = target.getAttribute("data-id")
			const name = target.getAttribute("data-name")
			setCurrentProject(id)
			setProjectName(name)
			setQueryEnabled(true)
		}
	}

	const handleSaveProjectReport = async (isPic = false, data) => {
		let report = { ...projectReportData }
		if (isPic) {
			report = data
		}
		try {
			showLoader()

			await app.saveProjectReport(RegistrationNumber, reportingYear, currentProject, report)

			hideLoader()
		} catch (error) {
			// handle error
		}
	}

	const getProjectReportData = async () => {
		try {
			setIsReportDataFetched(false)
			const result = await app.getProjectReport(RegistrationNumber, reportingYear, currentProject)
			if (result && result.length > 0) {
				setProjectReportData(result[0])
			} else {
				setProjectReportData({ ...data })
			}

			setIsReportDataFetched(true)
		} catch (error) {
			// handle error
		}
	}

	useEffect(() => {
		if (currentProject && currentProject !== "") {
			getProjectReportData()
		}
	}, [currentProject, reportingYear])

	useEffect(() => {
		if (yearlyData?.data?.success) {
			setYearlyEmissionData(yearlyData.data.Data)
		}
	}, [yearlyData.data])

	useEffect(() => {
		if (scope_Data.data && scope_Data.data.scope) {
			let { Scope_1, Scope_2, Scope_3, consumptionBased, locationBased, scope3IncludingDownStream } =
				scope_Data.data.scope

			setScopeData({
				Scope_1: parseFloat(Scope_1),
				Scope_2: parseFloat(Scope_2),
				Scope_3: parseFloat(Scope_3),
				total: Scope_1 + Scope_2 + Scope_3,
				isDefault: false,
			})

			setScope2Emission(parseFloat(Scope_2))

			// if scope3IncludingDownStream is not null, then set scope3EmissionIncludingDownStream and totalEmissionIncludingDownStream
			if (scope3IncludingDownStream) {
				setScope3EmissionIncludingDownStream(scope3IncludingDownStream)
			} else {
				setScope3EmissionIncludingDownStream(null)
			}

			const total = Scope_1 + Scope_2 + Scope_3

			setScopeDataLocationBased({
				Scope_1: parseFloat(Scope_1),
				Scope_2: locationBased ? parseFloat(locationBased) : 0,
				Scope_3: parseFloat(Scope_3),
				total: total,
			})

			// save this data in redux store because we need same data on other pages

			setScope2Data({
				combustionBased: consumptionBased ? Number(consumptionBased).toFixed(2) : 0,
				locationBased: locationBased ? Number(locationBased).toFixed(2) : 0,
				marketBased: Scope_2 ? Number(Scope_2).toFixed(2) : 0,
			})
		}
	}, [scope_Data.data])

	useEffect(() => {
		const getCo2Added = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */
				setIsTotalCo2Added(false)
				const res = CO2AddedData
				//		if (res.data.success) {

				const {
					totalNonRenewable,
					totalNuclear,
					totalRenewable,
					totalStationary,
					totalMobile,
					totalPrimaryScope,
					totalScope,
					scopePercentageWithStatus1,
				} = res.data

				let total = totalNonRenewable + totalRenewable + totalMobile + totalStationary + totalNuclear || 0

				let energyData = [
					{
						name: "Stationary combustion",
						value: totalStationary || 0,
						percentage: totalStationary ? (totalStationary / total) * 100 : 0,
					},
					{
						name: "Mobile combustion",
						value: totalMobile || 0,
						percentage: totalMobile ? (totalMobile / total) * 100 : 0,
					},
					{
						name: "Renewable electricity",
						value: totalRenewable || 0,
						percentage: totalRenewable ? (totalRenewable / total) * 100 : 0,
					},
					{
						name: "Non-Renewable electricity",
						value: totalNonRenewable || 0,
						percentage: totalNonRenewable ? (totalNonRenewable / total) * 100 : 0,
					},
					{
						name: "Nuclear energy",
						value: totalNuclear || 0,
						percentage: totalNuclear ? (totalNuclear / total) * 100 : 0,
					},
				]

				setEnergyChartValues([
					{ label: "Stationary combustion", value: totalStationary || 0, fill: "#78716c" },
					{ label: "Mobile combustion", value: totalMobile || 0, fill: "#f43f5e" },
					{ label: "Renewable electricity", value: totalRenewable || 0, fill: "#14b8a6" },
					{ label: "Non-Renewable electricity", value: totalNonRenewable || 0, fill: "#f59e0b" },
					{ label: "Nuclear energy", value: totalNuclear || 0, fill: "#84cc16" },
				])

				//setTotalCo2Added(co2Added)
				//setCo2Added(data)
				setTotalEnergy(total)
				setEnergyData(energyData)
				//	}
				setIsTotalCo2Added(true)

				setTotalPrimaryScope((totalPrimaryScope / totalScope) * 100)

				setScopePrimaryData(scopePercentageWithStatus1 || [])
			} catch (error) {
				// Handle Error
				setIsTotalCo2Added(true)
			}
		}

		//if (!scopeData.isDefault) {
		getCo2Added()
		//}
	}, [CO2AddedData.data])

	useEffect(() => {
		//setIsScopeCategoryData(false)
		const getScopeCategoryData = async () => {
			try {
				/* eslint react-hooks/exhaustive-deps: 0 */

				const res = scope_catagory_data.data
				//if (isUnmounted.current) return
				//	if (res.success) {
				if (res && res.categoryData && res.categoryData.length > 8) {
					for (let i = 8; i < res.categoryData.length; i++) {
						/* eslint react-hooks/exhaustive-deps: 0 */
						//colorCode.push(`#${generateColorCode()}`)
					}
				}

				setDownStreamCatagoryEmission(res?.downStreamTotalEmissionCatagory || 0)

				setScopeCategoryPrimaryData(res?.categoriesWithStatus1 || [])
				setScopeCategoryData(res.categoryData)
				//setIsScopeCategoryData(true)
			} catch (error) {
				// Handle Error
			}
		}
		getScopeCategoryData()
	}, [scope_catagory_data.data])

	useEffect(() => {
		scrollToTop?.current?.scrollIntoView()
	}, [])

	useEffect(() => {
		if (Analysis.data && Analysis.data.success) {
			setProjects(Analysis.data.projects)
			//setProjectDefaultValue(Analysis.data.projects[0])
			//setCurrentProject(Analysis.data.projects[0].value)
			setDepartments(Analysis.data.departments)
			//setQueryEnabled(true)
		} else {
			setProjects([])
			setDepartments([])
		}
	}, [Analysis.data])

	const isLoading =
		(scope_Data.isFetching ||
			scope_catagory_data.isFetching ||
			Analysis.isFetching ||
			CO2AddedData.isFetching ||
			yearlyData.isFetching) &&
		isCompanyRegistered &&
		queryEnabled &&
		isReportDataFetched

	useEffect(() => {
		if (projectEmission.data && projectEmission.data.length) {
			setEmissions(projectEmission.data)
		}
	}, [projectEmission.data])

	useEffect(() => {
		if (isLoading && skelton.current && queryEnabled) {
			skelton.current.scrollIntoView({ behavior: "smooth" })
		}
	}, [isLoading])

	useEffect(() => {
		if (projects.length > 0 && emissions.length > 0) {
			//let analysis = [...projects]

			// analysis is array of objects with key value pair each object has label as key and id as value
			// now I want a big object with key as id and value as label

			let projectsObj = projects.reduce((acc, item) => {
				acc[item.value] = item.label
				return acc
			}, {})

			let departmentsObj = departments.reduce((acc, item) => {
				acc[item.value] = item.label
				return acc
			}, {})

			// emisison is array of object and each object has _id and emission now I want
			// to compare _id in analysisObj and get names of projects

			let projectsEmissionData = []

			let departmentsEmissionData = []

			emissions.forEach((item) => {
				if (!item._id) return

				if (projectsObj[item._id]) {
					projectsEmissionData.push({
						...item,
						name: projectsObj[item._id],
					})
				}
				if (departmentsObj[item._id]) {
					departmentsEmissionData.push({
						...item,
						name: departmentsObj[item._id],
					})
				}
			})

			setProjectAnalysisEmissions(projectsEmissionData)

			setDepartmentAnalysisEmissions(departmentsEmissionData)

			setIsAnalysisEmissionAdded(true)
		} else {
			setIsAnalysisEmissionAdded(true)
		}
	}, [emissions, projects])

	if (!hasProjectAccess) {
		return (
			<AddOnAccessModal title={"You don't have access to projects fill out form bellow we will connect you soon"} />
		)
	}

	return (
		<div ref={scrollToTop} className='p-16 print:p-0'>
			<Badge variant='secondary' className='print:hidden' />
			<div className='xl:max-w-5xl 2xl:max-w-6xl mx-auto flex flex-row gap-4 print:hidden mb-5'>
				<div className='w-1/2'>
					<TableComponent
						loading={isAnalysisEmissionAdded}
						data={projectAnalysisEmissions}
						handleClick={handleClickOnLink}
						queryEnabled={queryEnabled}
						tableHeadings={["Project Name", "Emission tCo2e"]}
					/>
				</div>

				<div className='w-1/2'>
					<TableComponent
						loading={isAnalysisEmissionAdded}
						data={departmentAnalysisEmissions}
						handleClick={handleClickOnLink}
						queryEnabled={queryEnabled}
						tableHeadings={["Department Name", "Emission tCo2e"]}
					/>
				</div>
			</div>

			{isLoading ? (
				<Loader ref={skelton} />
			) : (
				<div className={`xl:max-w-5xl 2xl:max-w-6xl mx-auto`}>
					<section className='p-8 mt-10 shadow-lg break-after-page print:shadow-none bg-white/50 print:mt-0 print:p-0'>
						<div className='my-6'>
							<p className='text-slate-800 text-2xl'>
								{t("report:total_emission_for_reporting_year")} {" " + reportingYear + " "}
								{t("report:for") + " "}
								<span className='font-bold'>{projectName || ""}</span>
							</p>
							<p className={`text-md font-bold text-${getPrimaryDataColors(totalPrimaryScope || 0)}-500`}>
								{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO").format(totalPrimaryScope || "")} %{" "}
								<span className='text-slate-600'>primary data</span>
							</p>
						</div>

						<Flex flexDirection='row' justifyContent='start' alignItems='start' className='w-full h-full'>
							<Flex flexDirection='col' alignItems='center' className='w-1/2' justifyContent='center'>
								<Flex alignItems='center' flexDirection='col' className='w-fit'>
									<h1 className='text-slate-600 font-bold text-lg'>{t("report:Location")}</h1>

									<div className='w-[400px]'>
										<DonutChart
											chartData={[
												{ label: "Scope 1", value: scopeDataLocationBased?.Scope_1, fill: "#f43f5e" },
												{ label: "Scope 2", value: scopeDataLocationBased?.Scope_2, fill: "#0ea5e9" },
												{
													label: "Scope 3",
													value: scopeDataLocationBased.Scope_3 - (downStreamCatagoryEmission || 0),
													fill: "#f59e0b",
												},
											]}
											message='No data found'
										/>
									</div>
								</Flex>

								<Flex alignItems='center' className='w-fit' justifyContent='center' flexDirection='col'>
									<h1 className='text-slate-600 font-bold text-lg'>{t("report:Market")}</h1>

									<div className='w-[400px]'>
										<DonutChart
											chartData={[
												{ label: "Scope 1", value: scopeData?.Scope_1, fill: "#f43f5e" },
												{ label: "Scope 2", value: scope2Emission, fill: "#0ea5e9" },
												{
													label: "Scope 3",
													value: scopeData.Scope_3 - (downStreamCatagoryEmission || 0),
													fill: "#f59e0b",
												},
											]}
											message='No data found'
										/>
									</div>
								</Flex>
							</Flex>

							<Flex flexDirection='col' className='w-1/2 gap-5'>
								<Scope
									heading={t("Scope 1 emissions")}
									decorationColor='rose'
									primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_1 || 0)}
									primaryData={scopePrimaryData.Scope_1 || 0}
									colors={["rose", "slate"]}
									primaryDataLabel={t("common:primary")}
									value={scopeData.Scope_1 || 0}
									total={scopeData.total || 0}
									language={i18n.language === "en" ? "en" : "no-NO"}
								/>

								<Scope
									heading={t("Scope 2 emissions")}
									decorationColor='sky'
									primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_2 || 0)}
									colors={["sky", "slate"]}
									primaryDataLabel={t("common:primary")}
									primaryData={scopePrimaryData.Scope_2 || null}
									value={scopeData.Scope_2 || 0}
									total={scopeData.total || 0}
									select={true}
									dropDownWidth={250}
									selectedValue={scope2CalculationMethod}
									onChangeHandler={(e) => calculateEmission(e)}
									options={scope2Methods}
									language={i18n.language === "en" ? "en" : "no-NO"}
								/>

								<Scope
									heading={t("Scope 3 emissions")}
									decorationColor='amber'
									primaryData={scopePrimaryData.Scope_3 || null}
									primaryColor={getPrimaryDataColors(scopePrimaryData.Scope_3 || 0)}
									colors={["amber", "slate"]}
									value={scopeData.Scope_3}
									dwonStreamEmission={scope3EmissionIncludingDownStream}
									primaryDataLabel={t("common:primary")}
									total={scopeData.total}
									language={i18n.language === "en" ? "en" : "no-NO"}
								/>
							</Flex>
						</Flex>

						<div className='mt-2 flex justify-between break-inside-avoid'>
							<div className='w-2/3'>
								<BarChartComponent
									language={i18n.language === "en" ? "en" : "no-NO"}
									title={"Monthly chart data"}
									showLegend={false}
									height='240px'
									showCustomLegend={true}
									Data={monthlyChartData?.data?.EmissionData}
								/>
							</div>

							<div className='ml-2 w-1/3'>
								<BarChartComponent
									showLegend={false}
									language={i18n.language === "en" ? "en" : "no-NO"}
									title={"Yearly chart data"}
									index='_id'
									Data={yearlyEmissionData}
								/>
							</div>
						</div>
					</section>

					<section className='mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page'>
						<div className='border-b-2 border-darkBlack break-inside-avoid'>
							<div className='text-2xl font-bold print:text-3xl mb-5 mt-2'>
								{t("report:Scope1")}
								<PrimaryDataMetric value={scopePrimaryData?.Scope_1 || 0} t={t} />
							</div>

							<Flex flexDirection='row' className='gap-2' justifyContent='space-between' alignItems='center'>
								<ScopeMetricCard
									title={t("report:Market_based")}
									total={scopeData.total}
									colors={["rose", "slate"]}
									value={scopeData.Scope_1 || 0}
								/>

								<ScopeMetricCard
									title={t("report:Location_based")}
									showValue={false}
									total={scopeData.Scope_1 + scopeDataLocationBased.Scope_2 + scopeData.Scope_3}
									colors={["rose", "slate"]}
									value={scopeData.Scope_1 || 0}
								/>
							</Flex>

							<RichTextEditor
								html={projectReportData.SCOPE1_EMISSION}
								show={companyInfoEdit.scope1emission}
								handleImageUploadSuccess={handleImageUploadSuccess}
								handleSaveProjectReport={handleSaveProjectReport}
								path={path}
								handleChangeContent={(content) => handleChangeContent("SCOPE1_EMISSION", content.value)}
								handleOpenEditor={() => handleOpenEditor("scope1emission")}
							/>
						</div>

						{/* scope 2 emisson */}

						<div className='border-b-2 border-darkBlack break-inside-avoid'>
							<div className='text-2xl font-bold print:text-3xl mb-5 mt-2'>
								{t("report:Scope2")}
								<PrimaryDataMetric value={scopePrimaryData?.Scope_2 || 0} t={t} />
							</div>

							<div className='flex justify-between gap-2 items-center'>
								<ScopeMetricCard
									title={t("report:Market_based")}
									total={scopeData.total}
									colors={["sky", "slate"]}
									value={scope2Data?.marketBased ? parseFloat(scope2Data.marketBased).toFixed(2) : 0.0}
								/>
								<ScopeMetricCard
									showValue={true}
									title={t("report:Location_based")}
									total={scopeData.total}
									colors={["sky", "slate"]}
									value={scope2Data?.locationBased ? parseFloat(scope2Data.locationBased).toFixed(2) : 0.0}
								/>
							</div>

							<RichTextEditor
								html={projectReportData.SCOPE2_EMISSION}
								show={companyInfoEdit.scope2emission}
								handleImageUploadSuccess={handleImageUploadSuccess}
								handleSaveProjectReport={handleSaveProjectReport}
								path={path}
								handleChangeContent={(content) => handleChangeContent("SCOPE2_EMISSION", content.value)}
								handleOpenEditor={() => handleOpenEditor("scope2emission")}
							/>
						</div>

						{/* scope 3 emisson */}

						<div className='break-inside-avoid'>
							<div className='text-2xl font-bold print:text-3xl mb-5 mt-2'>
								{t("report:Scope3")}
								<PrimaryDataMetric value={scopePrimaryData?.Scope_3 || 0} t={t} />
							</div>

							<Flex flexDirection='row' className='gap-2' justifyContent='space-between' alignItems='center'>
								<ScopeMetricCard
									title={t("report:Market_based")}
									total={scopeData.total}
									colors={["amber", "slate"]}
									value={scopeData.Scope_3 || 0}
								/>

								<ScopeMetricCard
									showValue={false}
									title={t("report:Location_based")}
									total={scopeData.Scope_1 + scopeDataLocationBased.Scope_2 + scopeData.Scope_3}
									colors={["amber", "slate"]}
									value={scopeData.Scope_3 || 0}
								/>
							</Flex>

							<RichTextEditor
								html={projectReportData.SCOPE3_EMISSION}
								show={companyInfoEdit.scope3emission}
								handleSaveProjectReport={handleSaveProjectReport}
								handleImageUploadSuccess={handleImageUploadSuccess}
								path={path}
								handleChangeContent={(content) => handleChangeContent("SCOPE3_EMISSION", content.value)}
								handleOpenEditor={() => handleOpenEditor("scope3emission")}
							/>
						</div>
					</section>

					<section className='mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page'>
						<Scope3CategorySection
							t={t}
							categories={scopeCategoryData}
							scopeCategoryPrimaryData={scopeCategoryPrimaryData}
						/>
						<div className='print:break-inside-avoid-page'>
							<div className='mt-6 border-2 border-c_border px-2 py-2 rounded-md'>
								<div className='text-md font-bold print:text-lg'>{t("Category9_15")}</div>
								<div className='flex justify-between items-center'>
									<div>
										<h1 className='text-md font-bold print:text-lg'>
											{parseFloat(downStreamCatagoryEmission || 0).toFixed(2)} tCo2e
										</h1>
									</div>
								</div>
							</div>
						</div>

						<div className='break-inside-avoid'>
							<div className='flex mt-6 w-full flex-col gap-3 xl:mt-2'>
								<div className='ml-2 text-xl font-bold'>{t("Energy overview")}</div>
								<div className='overflow-hidden border-t-4 border-sky-600 shadow-md ring-1 ring-slate-200 sm:rounded-lg mb-3'>
									<table className='min-w-full divide-y divide-slate-300'>
										<thead className='bg-slate-100'>
											<tr>
												<th className='px-4 py-3 text-left text-base font-semibold text-slate-900 '>
													{t("Energy source")}
												</th>
												<th className='table-cell px-2 py-2 text-left text-base font-semibold text-slate-900'>
													{t("Percentage")}
												</th>
												<th className='float-right py-3 pl-2 pr-2 text-left text-base font-semibold text-slate-900'>
													{t("Kilowatt hours")}
												</th>
											</tr>
										</thead>
										<tbody className='divide-y divide-slate-200 bg-white'>
											{isTotalCo2Added ? (
												energyData.map((co2, index) => {
													return (
														<tr className='table-light' key={index}>
															<td
																className='truncate py-2.5 px-4 text-base text-slate-700 '
																style={{ maxWidth: "100px" }}
															>
																{t(co2.name)}
															</td>
															<td className='table-cell px-2 py-2 ml-4 text-base text-slate-700'>
																{/* Add your percentage value here */}
																{co2.percentage.toFixed(2) || 0} %
															</td>
															<td className='w-6 px-4 py-2 text-right text-base text-slate-700'>
																{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
																	maximumFractionDigits: 2,
																}).format(co2.value)}
															</td>
														</tr>
													)
												})
											) : (
												<tr>
													<td className='custom-td' colSpan={3}>
														<Spinner type='sm' />
													</td>
												</tr>
											)}
										</tbody>
										<tfoot>
											<tr className='border-t-2 border-slate-300'>
												<td className='px-4 text-left text-base font-semibold text-slate-900'>
													{t("Total energy to output")}
												</td>
												<td className='table-cell px-2 py-2 text-base font-semibold text-slate-900'>
													{/* Add your total percentage value here */}
													100%
												</td>
												<td className='w-6 px-4 py-2 text-right text-base font-semibold text-slate-900'>
													{Intl.NumberFormat(i18n.language === "en" ? "en" : "no-NO", {
														maximumFractionDigits: 2,
													}).format(totalEnergy)}
												</td>
											</tr>
										</tfoot>
									</table>
								</div>
							</div>
							<div className='w-full flex justify-center items-center mt-4'>
								<div className='w-[500px]'>
									<DonutChart showLabel={false} chartData={energyChartValues} innerRadius={0} />
								</div>

								<div className='-ml-24 z-50'>
									{energyChartValues.map(({ label, fill }, index) => {
										return (
											<div key={index} className='flex items-center gap-1'>
												<div
													className='h-2 w-2 shrink-0 rounded-[2px]'
													style={{
														backgroundColor: fill,
													}}
												/>
												<span className='text-sm'>{t(`${label}`)}</span>
											</div>
										)
									})}
								</div>
							</div>
						</div>
					</section>

					<section className='mt-12 py-8 px-7 print:mt-2 shadow-md print:shadow-none bg-white/50 print:break-before-page'>
						<div>
							<h1 className='text-2xl font-bold print:text-3xl'>{t("Methodology")}</h1>

							<RichTextEditor
								html={projectReportData.Metodikk}
								show={companyInfoEdit.Metodikk}
								handleSaveProjectReport={handleSaveProjectReport}
								handleImageUploadSuccess={handleImageUploadSuccess}
								path={path}
								handleChangeContent={(content) => handleChangeContent("Metodikk", content.value)}
								handleOpenEditor={() => handleOpenEditor("Metodikk")}
							/>
						</div>

						<div className='break-inside-avoid'>
							<h1 className='text-2xl font-bold print:text-3xl'>{t("References")}</h1>

							<RichTextEditor
								html={projectReportData.Referanser}
								show={companyInfoEdit.Referanser}
								handleImageUploadSuccess={handleImageUploadSuccess}
								path={path}
								handleSaveProjectReport={handleSaveProjectReport}
								handleChangeContent={(content) => handleChangeContent("Referanser", content.value)}
								handleOpenEditor={() => handleOpenEditor("Referanser")}
							/>
						</div>
					</section>
				</div>
			)}
			{loader}
		</div>
	)
}
