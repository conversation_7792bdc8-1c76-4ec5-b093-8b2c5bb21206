import React from "react"
import { useTranslation } from "react-i18next"
import { useDispatch } from "react-redux"

import Button from "../../../components/ui/Button.jsx"
import { openSubscriptionModal } from "../../../store/actions/UserAction"

export default function Subscription({ loginUser }) {
	const dispatch = useDispatch()
	const { t } = useTranslation("subscription")
	// const { tc } = useTranslation("common")

	return (
		<div className='cl-component cl-user-profile'>
			<div className='cl-main mt-4'>
				<div className='cl-user-profile-card cl-themed-card'>
					<div className='cl-titled-card-list'>
						<div className='font-extrabold text-2xl'>{t("Subscription")}</div>
						<p className='text-base my-2'>
							{t("CurrentSubscription")}{" "}
							<span className='ml-3 mt-0 bg-teal-600 px-2 py-0.5 text-xs text-white rounded-xl'>
								{loginUser?.Subscription === 1 ? "Enterprise" : "Free"}
							</span>
						</p>
						<Button
							title={"Add Reporting"}
							handleClick={() => window.open("https://myreportingapp.netlify.app/", "_blank")}
						/>
						<div className='w-full mt-3'>
							<div className='flex justify-end'>
								{loginUser?.Subscription === 1 ? (
									<></>
								) : (
									// <Button title={t("Edit")} />
									<Button title={t("SubscriptionInquiry")} handleClick={() => dispatch(openSubscriptionModal())} />
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
