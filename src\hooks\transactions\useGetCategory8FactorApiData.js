import { useQuery } from '@tanstack/react-query'
import axios from 'axios'

import { CATEGORY8_ENERGY_API_DATA } from '../../pages/transaction/EditDetails/Scope3/Category8/CATEGORY8_DATA'

const source = axios.CancelToken.source()

const getCategory8FactorApiData = async () => {
  try {
    const dataToSend = {
      energy: CATEGORY8_ENERGY_API_DATA
    }
    const res = await axios.post(`${import.meta.env.VITE_EMISSION_API_HOST}/emissions`, dataToSend, {
      headers: {
        'x-authorization': import.meta.env.VITE_EMISSION_API_SECRET
      },
      cancelToken: source.token
    })
    return res.data // Ensure you return res.data or another relevant part of the response
  } catch (error) {
    throw new Error('Failed to fetch category 8 factor data')
  }
}

const useGetCategory8FactorApiData = () => {
  return useQuery({
    queryKey: ['Category8FactorApiData'],
    queryFn: getCategory8FactorApiData
  })
}

export default useGetCategory8FactorApiData
