import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import Button from "../../../../components/ui/Button"
import Input from "../../../../components/ui/Input"
import useToast from "../../../../hooks/toast/useToast"

import { FACTOR_VALUE_HEAT, METRIC_LIST_HEAT, WTT_VALUE_HEAT } from "./constants/heat"

function HeatContent(props) {
	const { heatRows, updateHeatEmission, period } = props
	const unitInputRef = useRef()
	const { t } = useTranslation("transactions")

	const Toast = useToast()

	const [metric, setMetric] = useState(METRIC_LIST_HEAT[0])

	const [label, setLabel] = useState(METRIC_LIST_HEAT[0].label)

	const [factor, setFactor] = useState(FACTOR_VALUE_HEAT)

	const [unit, setUnit] = useState(0.0)

	const metricChange = (e) => {
		setMetric(e)
		setLabel(e.label)
		setFactor(FACTOR_VALUE_HEAT)
		unitInputRef.current.focus()
	}

	const addHeatRow = (scope2, scope3) => {
		const newHeatRow = {
			metric: label,
			factor: Number(factor),
			unit: Number(unit),
			scope2: Number(scope2),
			scope3: Number(scope3),
			scope1: 0,
		}

		const updatedHeatRows = [...heatRows, newHeatRow]

		updateHeatEmission(newHeatRow, false, updatedHeatRows)
	}

	const calculateHeatEmission = () => {
		// let renewable = this.state.renewable_energy
		//let non_renewable_energy = this.state.non_renewable_energy

		if (parseFloat(factor) == 0 || parseFloat(unit) == 0 || parseInt(period) == 0) {
			Toast("error", t("heat_error"))
			return
		}

		let scope2 = parseFloat(unit) * parseFloat(factor - WTT_VALUE_HEAT)
		let scope3 = parseFloat(unit) * WTT_VALUE_HEAT

		addHeatRow(scope2, scope3)
	}

	const deleteHeatRow = (index) => {
		let heatRow = heatRows[index]
		let newHeatRows = [...heatRows]
		newHeatRows.splice(index, 1)

		updateHeatEmission(heatRow, true, newHeatRows)
	}

	const handleFocus = (event) => event.target.select()

	return (
		<>
			{heatRows.map((heat, index) => (
				<div key={index}>
					<div className='grid grid-cols-5 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{heat.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{heat.factor}</span>
								<span className='custom-span-unit-value-save'>
									{t("Gram")} {t("Co2e")}
								</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{heat.unit}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kwh")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(heat.scope1 + heat.scope2 + heat.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteHeatRow(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			<div className='grid grid-cols-4 gap-10 my-1'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"metric-heat-"}>
						{t("Metric")}
					</label>
					<Select
						id={"metric-heat-"}
						value={metric}
						isDisabled={true}
						onChange={metricChange}
						options={METRIC_LIST_HEAT}
						maxMenuHeight={150}
					/>
				</div>
				<div>
					<Input
						label='Factor'
						id={"factor-heat-"}
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						type='number'
						unit='Gram Co2e'
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						id={"factor-heat-"}
						ref={unitInputRef}
						value={unit}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						type='number'
						unit={t("kwh")}
					/>
				</div>

				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateHeatEmission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default HeatContent
