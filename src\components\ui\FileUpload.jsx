import React from "react"

import { Progress } from "./progress"

export default function FileUpload({
	dropRef,
	selectRef,
	onSelectFile,
	openFileUpload,
	isDisable,
	fileName,
	isFileSelected,
	isFileUploading,
	uploadFile,
	progress,
	t,
}) {
	return (
		<div className='fileUploadContainer' ref={dropRef}>
			<div className='fileUploadContain'>
				<div className='grid justify-items-center fileUpload'>
					<div className='mb-2 dropText'>{t("DropHere")}</div>
					<div className='mb-2 fileInput'>
						<input type='file' id='file' className='fileInputTag' ref={selectRef} onChange={(e) => onSelectFile(e)} />
						<button
							type='button'
							className='uploadImg'
							onClick={openFileUpload}
							onKeyDown={(e) => e.key === "Enter" && openFileUpload()}
						>
							<img src={"/undraw_File_sync.svg"} alt='Upload file' />
						</button>
					</div>

					{isFileUploading && (
						<div className='w-4/5'>
							<Progress value={progress} className='h-2' />
							<p className='text-xs text-right mt-1 text-muted-foreground'>{progress.toFixed(0)}%</p>
						</div>
					)}

					<div className='mb-3'>{fileName}</div>
					{isFileSelected && progress === null && (
						<div className='mb-2'>
							<button
								className={"uploadBtn " + (isDisable ? "disabled" : "")}
								disabled={isDisable}
								onClick={uploadFile}
							>
								{t("Upload")}
							</button>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}
