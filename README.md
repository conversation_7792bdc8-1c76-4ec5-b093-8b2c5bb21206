# backend-app-Services

Backend code for the app in Realm. All environments have their own branch.

## NB. make sure to run the following before merging branches:

git config --global merge.ours.driver true

### This might not work so manually make sure that the following files not merge:

Readme.md
realm_config.json
triggers(folder) - all files....

### To move a folder the following is a handy command

while in 'to branch'
git checkout 'from branch' -- ./graphql/custom_resolvers/

This is the MAIN PRODUCTION branch with the Scope321 database
