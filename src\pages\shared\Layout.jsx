import { useUser, useClerk, useSession } from "@clerk/clerk-react"
import React, { useState, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { ToastContainer } from "react-toastify"

import Spinner from "../../components/ui/Spinner"
import { useRealmApp } from "../../realm/RealmAppProvider"
import {
	UserAction,
	currentOrganizationAction,
	fileUploaded,
	refetchOrg,
	isCompanyRegistered,
	handleProjectReportAccess,
} from "../../store/actions/UserAction"
import Routes from "../auth/Routes"

import Header from "./Header"
import Sidebar from "./Sidebar"

// eslint-disable-next-line import/no-unresolved
import SubscribeModal from "@/components/SubscribeModal.component"

const loading = (
	<div className='mainContainer'>
		<Spinner />
	</div>
)

export default function Layout(props) {
	const navigate = useNavigate()
	const [toggleSidebar, setToggleSidebar] = useState(true)
	const { user } = useUser()
	const { signOut } = useClerk()
	const { session } = useSession()
	const app = useRealmApp()
	const dispatch = useDispatch()
	const fileUploadedFlag = useSelector((state) => state.user.fileUploaded)
	const subscribe = useSelector((state) => state.subscription)

	const checkForAccess = (addOns = [], checkingFor) => {
		if (!addOns || addOns.length === 0) return false

		const index = addOns.findIndex((value) => value.toUpperCase() == checkingFor.toUpperCase())

		return index > -1
	}

	useEffect(() => {
		const getUser = async () => {
			try {
				const projectAccess = checkForAccess(user.publicMetadata?.addOns, "Projects")

				dispatch(handleProjectReportAccess(projectAccess))

				let token = await session.getToken({
					template: import.meta.env.VITE_CLERK_FRONTEND_JWT_TEMPLATE,
				}) // => "eyJhbGciOiJSUzI1NiIsImtpZC..."
				localStorage.setItem("email", user.primaryEmailAddress.emailAddress)
				localStorage.setItem("netlifyId", user.id)
				const res = await app.loginJWT(token)
				if (res) {
					// Get user data form user collection(user.id = netlifyId)
					const realmUser = await app.getRealmUserData(user.id)

					let orgCreated = !realmUser.organizationId ? false : true
					dispatch(UserAction({ ...realmUser, organizationCreated: orgCreated }))
					dispatch(
						currentOrganizationAction({
							CompanyName: realmUser.CompanyName,
							RegistrationNumber: realmUser.RegistrationNumber,
							company: realmUser.companyInfo,
							email: realmUser.email,
							name: realmUser.name,
							netlifyID: realmUser.netlifyID,
							organizationId: realmUser.organizationId,
							Subscription: realmUser.Subscription,
							organizationCreated: orgCreated,
						})
					)
					if (!realmUser.RegistrationNumber) {
						dispatch(isCompanyRegistered(false))
						//return
					} else {
						dispatch(isCompanyRegistered(true))
					}

					// if (realmUser.RegistrationNumber) {
					// 	//navigate("/dashboard")
					// } else {
					// 	navigate("/welcome")
					// }
					// Check user uploaded saf-t file, yes = redirect to dashboard, no = redirect to welcome page
					if (realmUser?.RegistrationNumber) {
						// return navigate(path);
					} else {
						const { data } = await user.getOrganizationMemberships()

						if (data && data.length) {
							try {
								let res = await app.getRealmUserByOrgId(data[0].organization.id)
								if (res.RegistrationNumber) {
									dispatch(
										currentOrganizationAction({
											CompanyName: res.CompanyName,
											RegistrationNumber: res.RegistrationNumber,
											company: res.companyInfo,
											email: res.email,
											name: res.name,
											netlifyID: res.netlifyID,
											organizationId: res.organizationId,
											Subscription: res.Subscription,
											organizationCreated: res.organizationId ? true : false,
										})
									)
									dispatch(isCompanyRegistered(true))
									/* eslint react-hooks/exhaustive-deps: 0 */
									//return navigate("/dashboard")
								} else {
									//return navigate("/welcome")
								}
							} catch (error) {
								/* eslint react-hooks/exhaustive-deps: 0 */
								dispatch(isCompanyRegistered(false))
								return navigate("/welcome")
							}
						} else {
							/* eslint react-hooks/exhaustive-deps: 0 */
							dispatch(isCompanyRegistered(false))
							return navigate("/welcome")
						}
					}
				} else {
					signOut()
				}
			} catch (e) {
				// handle error
			}
		}
		if (!user) {
			signOut()
		} else {
			getUser()
		}
	}, [])

	useEffect(() => {
		if (fileUploadedFlag) {
			const getUser = async () => {
				try {
					let token = await session.getToken({
						template: import.meta.env.VITE_CLERK_FRONTEND_JWT_TEMPLATE,
					}) // => "eyJhbGciOiJSUzI1NiIsImtpZC..."
					localStorage.setItem("email", user.primaryEmailAddress.emailAddress)
					localStorage.setItem("netlifyId", user.id)
					const res = await app.loginJWT(token)
					if (res) {
						// Get user data form user collection(user.id = netlifyId)
						const realmUser = await app.getRealmUserData(user.id)
						if (realmUser.CompanyName && realmUser.RegistrationNumber) {
							dispatch(refetchOrg(true))
							dispatch(UserAction(realmUser))
							dispatch(
								currentOrganizationAction({
									CompanyName: realmUser.CompanyName,
									RegistrationNumber: realmUser.RegistrationNumber,
									email: realmUser.email,
									name: realmUser.name,
									netlifyID: realmUser.netlifyID,
									Subscription: realmUser.Subscription,
									organizationCreated: false,
									company: realmUser.companyInfo,
								})
							)
							dispatch(isCompanyRegistered(true))
						} else {
							//dispatch(fileUploaded(true))
						}
					} else {
						signOut()
					}
				} catch (e) {
					// handle error
				}
			}
			if (!user) {
				signOut()
			} else {
				getUser()
			}
			dispatch(fileUploaded(false))
		}
	}, [fileUploadedFlag])

	return (
		<div>
			<div className='wrapper'>
				<div className='side-bar'>
					<Sidebar toggleSidebar={toggleSidebar} setToggleSidebar={setToggleSidebar} active={props.active} />
				</div>
				<div className='header'>
					<Header toggleSidebar={toggleSidebar} setToggleSidebar={setToggleSidebar} active={props.active} />
				</div>
				<div className='content overflow-y-auto bg-slate-50 !border-none !outline-transparent'>
					{/* {props.children} */}
					<SubscribeModal isOpen={subscribe} />
					<React.Suspense fallback={loading}>
						<Routes></Routes>
					</React.Suspense>
				</div>
			</div>

			<style jsx='true'>{`
				.wrapper {
					display: grid;
					grid-template-columns: auto 1fr;
					grid-template-rows: 56px auto;
					grid-column-gap: 0px;
					grid-row-gap: 0px;
					height: 100vh;
					overflow: hidden;
				}

				.header {
					grid-area: 1 / auto / auto / 3;
				}

				.side-bar {
					grid-area: 1 / 1 / 3 / 2;
				}

				.content {
					grid-area: 2 / 2 / 3 / 3;
				}

				@media print {
					.content {
						overflow: visible !important;
						background-color: white !important;
					}
				}
			`}</style>
			<ToastContainer position='top-right' autoClose={3000} draggable={false} />
		</div>
	)
}
