import { useQuery } from "@tanstack/react-query"

export default function useGetScopeChartData(app, input,enabled=true) {
	return useQuery({
		queryKey: ["scopeData", input],
		queryFn: async () => {
			const { RegistrationNumber, year, AnalysisIDs } = input
			if (input.RegistrationNumber !== "") return await app.getChartScopeData(RegistrationNumber, year, AnalysisIDs)
		},

		enabled: (input !== undefined && enabled),
		//refetchOnWindowFocus: false,
		refetchOnMount: true,
		retry: 5,
	})
}
