/* eslint-disable import/no-unresolved */
import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import { METRIC_LIST_COMBUSTION, FACTORS_DATA_COMBUSTION } from "./constants/combustion"

import Button from "@/components/ui/Button"
import Input from "@/components/ui/Input"
import { useToast } from "@/hooks"

function CombustionContent(props) {
	const { combustionRows, updateScopeAndCombustion } = props
	const unitInputRef = useRef()
	const { t } = useTranslation("transactions")
	const toast = useToast()

	const defaultMetric = FACTORS_DATA_COMBUSTION["PetrolForecourt"]

	const [metric, setMetric] = useState({
		name: "Petrol bio-blend",
		value: "PetrolForecourt",
		label: "Petrol bio-blend",
	})
	const [label, setLabel] = useState("Petrol bio-blend")

	const [factor, setFactor] = useState(Number(defaultMetric.scope1 + defaultMetric.scope3).toFixed(2))

	const [unit, setUnit] = useState(Number(0.0).toFixed(2))

	const [unitType, setUnitType] = useState(defaultMetric.unit)

	const getFactor = (metric) => {
		const data = FACTORS_DATA_COMBUSTION[metric.value].scope1 + FACTORS_DATA_COMBUSTION[metric.value].scope3
		return data
	}

	const addCombustionRow = (scope1, scope2, scope3, stationary, kwh) => {
		const newCombustionRow = {
			metric: label,
			factor: Number(factor),
			unit: Number(unit),
			unitType: unitType,
			scope1: Number(scope1),
			scope2: Number(scope2),
			scope3: Number(scope3),
			stationary: Number(stationary),
			kwh: Number(kwh),
		}

		const updatedCombustions = [...combustionRows, newCombustionRow]

		updateScopeAndCombustion(newCombustionRow, false, updatedCombustions)
	}

	const calculateCombustionEmissions = () => {
		if (parseFloat(factor) <= 0 && parseFloat(unit) <= 0) {
			toast("error", t("factor_unit_greater_0_error"))
			return
		}

		const metricValue = metric.value
		const factorData = FACTORS_DATA_COMBUSTION[metricValue]
		let scope1 = (Number(factor) * Number(unit)).toFixed(2)
		let scope3 = (factorData.scope3 * unit).toFixed(2)
		let kwh = factorData.kwh ? factorData.kwh : 10
		let stationary = kwh * unit

		if (Number(factor) === 0) {
			scope1 = 0
			scope3 = 0
		}

		//console.log(scope1, scope3, kwh, stationary)

		// if factor is 2.81 and distance is 10 for scope 1 we multiply factor with unit
		// for scope 3 we multiply factorData.scope3 with unit
		// for factor 2.81 and unit 10
		// total scope should be 28.1
		// if we multiply factor with unit we get 28.1
		// scope 3 is 10 * 0.61328 = 6.1328
		// scope 1 is 28.1 - 6.1328 = 21.9672 beacuse scope 3 is already included in scope 1

		addCombustionRow(scope1 - scope3, 0, scope3, stationary, kwh)
	}

	const metricChange = (event) => {
		setMetric(event)
		setLabel(event.label)
		setFactor(Number(getFactor(event)).toFixed(2))
		setUnitType(FACTORS_DATA_COMBUSTION[event.value].unit)

		unitInputRef.current.focus()
	}

	const deleteCombustionField = (index) => {
		let combustionRow = combustionRows[index]

		let newCombustionRows = [...combustionRows]

		newCombustionRows.splice(index, 1)

		updateScopeAndCombustion(combustionRow, true, newCombustionRows)

		//props.deleteCombustion(index)
	}

	const handleFocus = (event) => {
		event.target.select()
	}

	return (
		<>
			{combustionRows.map((combustionRow, index) => (
				<div key={index} className='mb-2'>
					<div className='grid grid-cols-6 gap-8 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Metric")}</label>
							<div>{combustionRow.metric}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Factor")}</label>
							<div className='flex relative'>
								<span>{combustionRow.factor}</span>
								<span className='custom-span-unit-value-save'>{t("Kg-Co2e")}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Units")}</label>
							<div className='flex relative'>
								<span>{combustionRow.unit}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{combustionRow.unitType}</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(combustionRow.scope1 + combustionRow.scope2 + combustionRow.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("KWH")}</label>
							<div className='flex relative'>
								<span>{Number(combustionRow.stationary).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kwh")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon justify-self-end self-center'>
							<span aria-hidden onClick={() => deleteCombustionField(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}

			<div className='grid grid-cols-4 gap-12'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"metric-combustionRow-"}>
						{t("Metric")}
					</label>
					<Select
						id={"metric-combustionRow-"}
						value={metric || null}
						onChange={metricChange}
						options={METRIC_LIST_COMBUSTION}
						maxMenuHeight={150}
					/>
				</div>

				<div>
					<Input
						label={t("Factor")}
						id={"factor-combustion-"}
						value={factor || ""}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						labelColor='text-sky-500'
						unit={t("Kg-Co2e")}
					/>
				</div>
				<div>
					<Input
						label={t("Units")}
						id={"unit-combustionRow"}
						ref={unitInputRef}
						value={unit || ""}
						handleChange={(e) => setUnit(e.target.value)}
						handleFocus={handleFocus}
						labelColor='text-sky-500'
						unit={unitType}
					/>
				</div>
				<div className='justify-self-end self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateCombustionEmissions()} color='sky-500' />
				</div>
			</div>
		</>
	)
}

export default CombustionContent
