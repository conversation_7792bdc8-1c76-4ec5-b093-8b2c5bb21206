import React from "react"

export default function TextArea({
	placeholder,
	name,
	value,
	handleChange,
	disable,
	id,
	label,
	type,
	className,
	rows,
}) {
	return (
		<>
			<label className="font-semibold block text-sm leading-4 mb-1 text-slate-800">{label}</label>
			<textarea
				id={id}
				name={name}
				type={type}
				placeholder={placeholder}
				rows={rows}
				className={
					className
						? className
						: "block w-full rounded-md border-0 px-3 py-1.5 text-gray-900 shadow-md ring-1 ring-gray-400 placeholder:text-gray-400 focus:ring-2 focus:ring-sky-600 sm:text-sm sm:leading-6"
				}
				disabled={disable}
				value={value}
				onChange={(e) => {
					handleChange(e)
				}}
			/>
		</>
	)
}
