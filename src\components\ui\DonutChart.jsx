"use client"

import * as React from "react"
import { Label, Pie, PieChart } from "recharts"

import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "./chart"

export default function DonutChart({
	chartData = [],
	message = "No data found",
	innerRadius = 64,
	showLabel = true,
	showLegend = false,
}) {
	const totalEmission = React.useMemo(() => {
		return chartData.reduce((acc, curr) => acc + curr.value, 0)
	}, [chartData])

	const getChartConfig = () => {
		return chartData.reduce((acc, { label, fill }) => {
			acc[label] = { label, fill }
			return acc
		}, {})
	}

	return (
		<ChartContainer config={getChartConfig()} className='max-h-[250px]'>
			{totalEmission ? (
				<PieChart>
					<ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
					<Pie
						data={chartData}
						dataKey='value'
						nameKey='label'
						innerRadius={innerRadius}
						strokeWidth={8}
						// Add fill property here
						fill='#8884d8'
					>
						{chartData.map((entry, index) => (
							<Label key={`slice-${index}`} fill={entry.fill} />
						))}

						{showLabel && (
							<Label
								content={({ viewBox }) => {
									if (viewBox && "cx" in viewBox && "cy" in viewBox) {
										return (
											<text x={viewBox.cx} y={viewBox.cy} textAnchor='middle' dominantBaseline='middle'>
												<tspan x={viewBox.cx} y={viewBox.cy} className='fill-foreground text-2xl font-bold'>
													{totalEmission.toFixed(2)}
												</tspan>
												<tspan x={viewBox.cx} y={(viewBox.cy || 0) + 24} className='fill-muted-foreground'>
													tCO2e
												</tspan>
											</text>
										)
									}
								}}
							/>
						)}
					</Pie>
					{showLegend && (
						<ChartLegend
							content={<ChartLegendContent nameKey='label' />}
							className='-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center'
						/>
					)}
				</PieChart>
			) : (
				<div className='flex items-center justify-center h-40 w-40 mx-auto ring-1 ring-gray-300 rounded-lg m-6'>
					<p className='text-gray-500'>{message}</p>
				</div>
			)}
		</ChartContainer>
	)
}
