import path from "path"

import react from "@vitejs/plugin-react"
// eslint-disable-next-line import/namespace
import { defineConfig } from "vite"
import eslint from "vite-plugin-eslint"

export default defineConfig(() => {
	return {
		resolve: {
			alias: {
				"@": path.resolve(__dirname, "src"), // Adjust based on your folder structure
			},
			extensions: [".mjs", ".js", ".jsx", ".ts", ".tsx", ".json"],
		},
		server: {
			port: 3000,
			proxy: {
				"/.netlify/functions": {
					target: "http://localhost:8888",
					changeOrigin: true,
				},
			},
		},
		build: {
			outDir: "dist",
		},
		plugins: [
			react(),
			eslint({
				failOnError: false,
				failOnWarning: false,
				emitWarning: false,
				emitError: false,
				lintOnStart: false,
			}),
		],
	}
})
