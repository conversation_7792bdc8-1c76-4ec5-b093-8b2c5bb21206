import React, { forwardRef } from "react"

const Input = forwardRef(function Input(
	{
		placeholder,
		name,
		value,
		handleChange,
		disable,
		id,
		label,
		type,
		hasError = false,
		errorMessage = "",
		unit,
		handleFocus = () => {},
		className,
		required = false,
	},
	ref
) {
	return (
		<>
			<label className={`font-semibold block text-sm leading-4 mb-1 text-slate-800`}>
				{label}
				{required && <span className='text-red-600 '>*</span>}
			</label>

			<div className='relative rounded w-full shadow-sm'>
				<input
					id={id}
					name={name}
					type={type}
					placeholder={placeholder}
					disabled={disable}
					ref={ref}
					onFocus={(e) => {
						//console.log(e)
						handleFocus(e)
					}}
					className={`block w-full rounded border-none py-1.5 pl-2 text-gray-900 ring-1 ring-gray-400 focus:outline-none placeholder:text-gray-400 focus:ring-2 focus:ring-sky-600 sm:text-sm sm:leading-6 disabled:bg-slate-100 disabled:ring-dashed disabled:text-gray-500 ${className}`}
					onChange={(e) => {
						handleChange(e)
					}}
					value={value}
				/>

				{unit && (
					<div className='pointer-events-none bg-slate-100 absolute inset-y-0 right-0 flex items-center px-2 rounded-r'>
						<span className='text-slate-600 sm:text-sm'>{unit}</span>
					</div>
				)}
			</div>
			{hasError && <span className='text-rose-600'>{errorMessage}</span>}
		</>
	)
})

export default Input
