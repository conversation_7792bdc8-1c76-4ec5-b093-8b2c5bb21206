import { useQuery } from "@tanstack/react-query"

export default function useGetTopSuppliers(app, input) {
	return useQuery({
		queryKey: ["topSuppliers", input],
		queryFn: async () => {
			const { RegistrationNumber, year } = input
			if (input.RegistrationNumber !== "") return await app.getTopSuppliers(RegistrationNumber, year)
		},

		enabled: input !== undefined,
		refetchOnMount: true,
		retry: 5,
	})
}
