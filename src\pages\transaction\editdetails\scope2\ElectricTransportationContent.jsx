import React, { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import Button from "../../../../components/ui/Button"
import Input from "../../../../components/ui/Input"
import { useToast } from "../../../../hooks"

import { co2_mix, ELECTRIC_TRANSPORT } from "./constants/electricTransportation"

export default function ElectricTransportationContent(props) {
	const { electricTransportRows, updateElectricTransportEmission, PeriodYear } = props
	const distanceInputRef = useRef()
	const { t } = useTranslation("transactions")
	const Toast = useToast()

	const [vehicleType, setVehicleType] = useState(ELECTRIC_TRANSPORT[0])

	const [vehicle, setVehicle] = useState(ELECTRIC_TRANSPORT[0].label)

	const [distance, setDistance] = useState(0)

	const [factor, setFactor] = useState(ELECTRIC_TRANSPORT[0].factor)

	const transportTypeChange = (event) => {
		setVehicleType(event)
		setVehicle(event.label)
		setFactor(event.factor)
		distanceInputRef.current.focus()
	}

	const addElectricTransportRow = (
		scope2,
		scope3,
		kwh,
		locationBased,
		consumptionBased,
		marketBased,
		renewable_energy,
		non_renewable_energy
	) => {
		const newElectricTransportRow = {
			type: vehicle,
			factor: Number(factor),
			distance: Number(distance),
			scope1: 0,
			scope2: Number(scope2),
			scope3: Number(scope3),
			kwh: Number(kwh),
			locationBased: Number(locationBased),
			consumptionBased: Number(consumptionBased),
			marketBased: Number(marketBased),
			renewable_energy: Number(renewable_energy),
			non_renewable_energy: Number(non_renewable_energy),
		}

		const updatedElectricTransportRows = [...electricTransportRows, newElectricTransportRow]

		updateElectricTransportEmission(newElectricTransportRow, false, updatedElectricTransportRows)
	}

	const calculateElectricTransportEmission = () => {
		if (parseFloat(factor) <= 0 && parseFloat(distance) <= 0) {
			Toast("error", t("factor_distance_error"))
			return
		}

		let newfactor = factor - 0.014
		let kwh = (distance * newfactor) / 100
		let renewable = kwh * 0.98
		let non_renewable_energy = kwh * 0.02

		let scope2 = kwh * co2_mix[PeriodYear]["NO"]["consumption"]
		let scope3 = 0.014
		//let s = factorData.kwh ? factorData.kwh : 10

		const co2Mix = co2_mix[PeriodYear]

		let location_Based = (kwh * co2Mix?.["NO"]?.co2) / 1000
		let market_Based = parseFloat(scope2) / 1000
		let consumption_Based = (kwh * co2Mix?.["NO"]?.consumption) / 1000

		addElectricTransportRow(
			scope2,
			scope3,
			kwh,
			location_Based,
			consumption_Based,
			market_Based,
			renewable,
			non_renewable_energy
		)
	}

	const deleteTransportRow = (index) => {
		let transportRow = electricTransportRows[index]
		let newTransportRows = [...electricTransportRows]
		newTransportRows.splice(index, 1)

		updateElectricTransportEmission(transportRow, true, newTransportRows)
	}

	const handleFocus = (event) => event.target.select()
	return (
		<>
			{electricTransportRows.map((electricity, index) => (
				<div key={index}>
					<div className='grid grid-cols-4 gap-10 my-1 saved-emission'>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("VehicleType")}</label>
							<div>{electricity.label}</div>
						</div>

						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Distance")}</label>
							<div className='flex relative'>
								<span>{electricity.distance}</span>
								<span className='text-nowrap custom-span-unit-value-save'>Km</span>
							</div>
						</div>

						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative'>
								<span>{Number(electricity.scope1 + electricity.scope2 + electricity.scope3).toFixed(2)}</span>
								<span className='text-nowrap custom-span-unit-value-save'>{t("kg")}</span>
							</div>
						</div>

						<div className='!items-center delete-icon'>
							<span aria-hidden onClick={() => deleteTransportRow(index)}>
								<RiDeleteBin6Line />
							</span>
						</div>
					</div>
					<hr />
				</div>
			))}
			<div className='grid grid-cols-4 gap-10 my-2'>
				<div>
					<label className='text-slate-800 font-semibold whitespace-nowrap' htmlFor={"vehicle-type"}>
						{t("VehicleType")}
					</label>
					<Select id={"vehicle-type"} value={vehicleType} onChange={transportTypeChange} options={ELECTRIC_TRANSPORT} />
				</div>
				<div>
					<Input
						label={t("factor")}
						type='number'
						value={factor}
						handleChange={(e) => setFactor(e.target.value)}
						handleFocus={handleFocus}
						unit={t("kwh/100km")}
					/>
				</div>

				<div>
					<Input
						label={t("Distance")}
						ref={distanceInputRef}
						type='number'
						value={distance}
						placeholder='distance'
						handleChange={(e) => setDistance(e.target.value)}
						handleFocus={handleFocus}
						unit={t("km")}
					/>
				</div>

				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={() => calculateElectricTransportEmission()} color='sky-500' />
				</div>
			</div>
		</>
	)
}
