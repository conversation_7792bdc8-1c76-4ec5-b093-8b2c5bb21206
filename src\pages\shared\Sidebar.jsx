/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-no-comment-textnodes */
import { useClerk, useUser } from "@clerk/clerk-react"
import { useQueryClient } from "@tanstack/react-query"
import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import {
	RiDashboard3Line,
	RiTruckLine,
	RiShoppingCart2Line,
	RiArrowRightSFill,
	RiPrinterFill,
	RiLogoutCircleLine,
	RiUploadCloud2Line,
	RiSettings2Line,
	RiProductHuntFill,
} from "react-icons/ri"
import { useDispatch, useSelector } from "react-redux"
import { Link, useLocation } from "react-router-dom"
import "../../styles/sidebar.css"

import { useRealmApp } from "../../realm/RealmAppProvider"
import { currentOrganizationAction, refetchOrg } from "../../store/actions/UserAction"
import { store } from "../../store/configureStore"

function Sidebar(props) {
	const { t } = useTranslation("sidebar")
	const app = useRealmApp()
	const [userData, setUser] = useState({})
	const [orgList, setOrgList] = useState([])
	const [activeComponent, setActiveComponent] = useState("")
	const location = useLocation()
	const isUnmounted = useRef(false)
	const { signOut } = useClerk()
	const { user } = useUser()
	const currentOrganization = useSelector((state) => state.user.currentOrganization)
	const refetchOrgFlag = useSelector((state) => state.user.refetchOrg)
	const [organizationId, setOrganizationId] = useState()
	const dispatch = useDispatch()
	const queryClient = useQueryClient()

	const logout = async () => {
		localStorage.removeItem("token")
		localStorage.removeItem("userId")
		localStorage.removeItem("email")
		localStorage.removeItem("netlifyId")
		queryClient.clear()
		app.logout()
		signOut()
	}

	const toggleSubLinkContainer = (value) => {
		let subLinkId = value + "-sub-link"
		let arrowId = value + "-arrow"
		document.getElementById(subLinkId).classList.toggle("sub-menu-link-show")
		document.getElementById(arrowId).classList.toggle("arrow-up")
	}

	const handleStoreValueChange = () => {
		const stateData = store.getState()
		if (stateData.user) {
			//if (isUnmounted.current) return
			setUser(stateData.user)
		}
	}

	const getOrgList = async () => {
		const { data } = await user.getOrganizationMemberships()
		setOrgList(data)
		dispatch(refetchOrg(false))
	}

	useEffect(() => {
		setOrganizationId(currentOrganization.organizationId)
	}, [currentOrganization.organizationId])

	useEffect(() => {
		if (refetchOrgFlag) {
			getOrgList()
		}
	}, [refetchOrgFlag])

	useEffect(() => {
		/* eslint react-hooks/exhaustive-deps: 0 */
		const unsubscribe = store.subscribe(handleStoreValueChange)
		getOrgList()
		return () => {
			isUnmounted.current = true
			unsubscribe()
		}
	}, [])

	useEffect(() => {
		let path = window.location.pathname
		if (path.length > 1) {
			setActiveComponent(path.substr(1))
		}
	}, [location])

	//  Sidebar items data(name, route link, icon)
	const data = [
		{
			name: "welcome",
			title: t("Welcome"),
			link: "/welcome",
			icon: <RiUploadCloud2Line className='linkIcon' />,
			isExpand: false,
		},
		{
			name: "dashboard",
			title: t("Dashboard"),
			link: "/dashboard",
			icon: <RiDashboard3Line className='linkIcon' />,
			isExpand: false,
		},
		{
			name: "suppliers",
			title: t("Suppliers"),
			link: "/suppliers",
			icon: <RiTruckLine className='linkIcon' />,
			isExpand: false,
		},
		{
			name: "transactionLabel",
			title: t("Transactions"),
			link: "/transactions/scope1",
			icon: <RiShoppingCart2Line className='linkIcon' />,
			isExpand: true,
			expandLink: [
				{
					name: "transactions/scope1",
					title: t("Scope1"),
					link: "/transactions/scope1",
					icon: <RiArrowRightSFill className='linkIcon' />,
					isExpand: true,
				},
				{
					name: "transactions/scope2",
					title: t("Scope2"),
					link: "/transactions/scope2",
					icon: <RiArrowRightSFill className='linkIcon' />,
					isExpand: true,
				},
				{
					name: "transactions/scope3",
					title: t("Scope3"),
					link: "/transactions/scope3",
					icon: <RiArrowRightSFill className='linkIcon' />,
					isExpand: true,
				},
				{
					name: "transactions",
					title: t("All"),
					link: "/transactions",
					icon: <RiArrowRightSFill className='linkIcon' />,
					isExpand: true,
				},
			],
		},
		{
			name: "report",
			title: t("Report"),
			link: "/report",
			icon: <RiPrinterFill className='linkIcon' />,
			isExpand: false,
		},
		{
			name: "Project",
			title: t("Project"),
			link: "/project",
			icon: <RiProductHuntFill className='linkIcon' />,
			isExpand: false,
		},
		{
			name: "Settings",
			title: t("Settings"),
			link: "/settings",
			icon: <RiSettings2Line className='linkIcon' />,
			isExpand: false,
		},
	]

	const changeOrganization = async (organizationId) => {
		try {
			const res = await app.getRealmUserByOrgId(organizationId)

			dispatch(
				currentOrganizationAction({
					CompanyName: res.CompanyName,
					RegistrationNumber: res.RegistrationNumber,
					company: res.companyInfo,
					email: res.email,
					name: res.name,
					netlifyID: res.netlifyID,
					organizationId: res.organizationId,
					Subscription: res.Subscription,
					organizationCreated: res.organizationId ? true : false,
				})
			)

			setOrganizationId(organizationId)
		} catch (e) {
			//handle error here
		}
	}

	if (props.toggleSidebar) {
		// Expanded Sidebar
		return (
			<div className='relative h-full w-16 bg-blue_d items-start flex flex-col justify-between print:hidden sidebarMenuToggle'>
				<div className='flex flex-col w-100'>
					<div className='h-[56px] w-full mb-4 flex items-center pl-3'>
						<svg
							className='-rotate-45 flex-none'
							width='34'
							height='34'
							viewBox='0 0 34 34'
							fill='none'
							xmlns='http://www.w3.org/2000/svg'
						>
							<g clipPath='url(#clip0)'>
								<path
									d='M28 23.3846C28 22.8718 27.8205 22.4359 27.4615 22.0769L23.4615 18.0769C23.1026 17.7179 22.6667 17.5384 22.1538 17.5384C21.6154 17.5384 21.1538 17.7436 20.7692 18.1538C20.8077 18.1923 20.9295 18.3109 21.1346 18.5096C21.3397 18.7083 21.4776 18.8461 21.5481 18.9231C21.6186 19 21.7147 19.1218 21.8365 19.2884C21.9583 19.4551 22.0417 19.6186 22.0865 19.7788C22.1314 19.9391 22.1538 20.1154 22.1538 20.3077C22.1538 20.8205 21.9743 21.2564 21.6154 21.6154C21.2564 21.9743 20.8205 22.1538 20.3077 22.1538C20.1154 22.1538 19.9391 22.1314 19.7788 22.0865C19.6186 22.0417 19.4551 21.9583 19.2884 21.8365C19.1218 21.7147 19 21.6186 18.9231 21.5481C18.8461 21.4776 18.7083 21.3397 18.5096 21.1346C18.3109 20.9295 18.1923 20.8077 18.1538 20.7692C17.7308 21.1667 17.5192 21.6346 17.5192 22.1731C17.5192 22.6859 17.6987 23.1218 18.0577 23.4808L22.0192 27.4615C22.3654 27.8077 22.8013 27.9808 23.3269 27.9808C23.8397 27.9808 24.2756 27.8141 24.6346 27.4808L27.4615 24.6731C27.8205 24.3141 28 23.8846 28 23.3846ZM14.4808 9.82691C14.4808 9.31409 14.3013 8.87819 13.9423 8.51922L9.98076 4.53845C9.62178 4.17947 9.18588 3.99999 8.67306 3.99999C8.17306 3.99999 7.73717 4.17306 7.36537 4.51922L4.53845 7.32691C4.17947 7.68588 3.99999 8.11537 3.99999 8.61537C3.99999 9.12819 4.17947 9.56409 4.53845 9.92306L8.53845 13.9231C8.8846 14.2692 9.3205 14.4423 9.84614 14.4423C10.3846 14.4423 10.8461 14.2436 11.2308 13.8461C11.1923 13.8077 11.0705 13.6891 10.8654 13.4904C10.6602 13.2917 10.5224 13.1538 10.4519 13.0769C10.3814 13 10.2852 12.8782 10.1634 12.7115C10.0417 12.5449 9.95832 12.3814 9.91345 12.2211C9.86858 12.0609 9.84614 11.8846 9.84614 11.6923C9.84614 11.1795 10.0256 10.7436 10.3846 10.3846C10.7436 10.0256 11.1795 9.84614 11.6923 9.84614C11.8846 9.84614 12.0609 9.86858 12.2211 9.91345C12.3814 9.95832 12.5449 10.0417 12.7115 10.1634C12.8782 10.2852 13 10.3814 13.0769 10.4519C13.1538 10.5224 13.2917 10.6602 13.4904 10.8654C13.6891 11.0705 13.8077 11.1923 13.8461 11.2308C14.2692 10.8333 14.4808 10.3654 14.4808 9.82691ZM31.6923 23.3846C31.6923 24.9231 31.1474 26.2243 30.0577 27.2884L27.2308 30.0961C26.1667 31.1602 24.8654 31.6923 23.3269 31.6923C21.7756 31.6923 20.4679 31.1474 19.4038 30.0577L15.4423 26.0769C14.3782 25.0128 13.8461 23.7115 13.8461 22.1731C13.8461 20.5961 14.4102 19.2564 15.5384 18.1538L13.8461 16.4615C12.7436 17.5897 11.4102 18.1538 9.84614 18.1538C8.30768 18.1538 6.99999 17.6154 5.92306 16.5384L1.92306 12.5384C0.84614 11.4615 0.307678 10.1538 0.307678 8.61537C0.307678 7.07691 0.85255 5.77563 1.94229 4.71152L4.76922 1.90383C5.83332 0.83973 7.1346 0.307678 8.67306 0.307678C10.2243 0.307678 11.532 0.85255 12.5961 1.94229L16.5577 5.92306C17.6218 6.98717 18.1538 8.28845 18.1538 9.82691C18.1538 11.4038 17.5897 12.7436 16.4615 13.8461L18.1538 15.5384C19.2564 14.4102 20.5897 13.8461 22.1538 13.8461C23.6923 13.8461 25 14.3846 26.0769 15.4615L30.0769 19.4615C31.1538 20.5384 31.6923 21.8461 31.6923 23.3846Z'
									fill='white'
								/>
							</g>
							<defs>
								<clipPath id='clip0'>
									<rect width='32' height='32' fill='white' />
								</clipPath>
							</defs>
						</svg>
						<svg
							width='100'
							height='20'
							className='pl-1'
							viewBox='0 0 200 32'
							fill='none'
							xmlns='http://www.w3.org/2000/svg'
						>
							<path
								d='M0 31.5527L0.0473762 24.5423L7.12072 24.5902C7.56307 24.5856 8.00477 24.5548 8.44356 24.498C8.95129 24.4385 9.4496 24.3145 9.92675 24.129C10.38 23.9558 10.7922 23.6881 11.1366 23.3431C11.3033 23.1734 11.434 22.971 11.5205 22.7483C11.6069 22.5255 11.6475 22.2872 11.6395 22.048C11.661 21.6733 11.5958 21.2987 11.449 20.9541C11.3022 20.6094 11.078 20.3043 10.7941 20.0629C10.1575 19.5581 9.44009 19.1675 8.67315 18.9081C7.82041 18.5981 6.89116 18.2845 5.88901 17.9598C4.89721 17.6421 3.95854 17.1743 3.10485 16.5725C2.21505 15.9295 1.48825 15.0824 0.983934 14.1004C0.4203 13.0574 0.138481 11.6713 0.138481 9.94203C0.12264 8.64171 0.37071 7.35195 0.867317 6.15267C1.3374 5.04407 2.01987 4.0408 2.87527 3.20089C3.74395 2.3595 4.7681 1.69997 5.88901 1.2601C7.0819 0.791025 8.35192 0.555629 9.63157 0.566425H18.6218V7.51788L10.4989 7.56585C9.85365 7.56153 9.22093 7.74603 8.6768 8.09717C8.40629 8.26018 8.18469 8.49464 8.03575 8.77543C7.88681 9.05623 7.81616 9.37278 7.83134 9.69113C7.81581 10.0373 7.89006 10.3816 8.04669 10.6897C8.20333 10.9978 8.43689 11.2591 8.72417 11.4474C9.41846 11.8949 10.1701 12.2437 10.9581 12.4843C11.8545 12.7794 12.8202 13.0783 13.8734 13.3882C14.9186 13.6938 15.9046 14.178 16.7888 14.8198C17.7214 15.5051 18.4868 16.3974 19.0263 17.4285C19.6215 18.5255 19.9179 19.9818 19.9155 21.7971C19.9614 23.3643 19.6496 24.9212 19.0044 26.3466C18.431 27.5521 17.5676 28.5924 16.4936 29.3721C15.3646 30.1724 14.0939 30.7449 12.751 31.0583C11.2567 31.4201 9.72487 31.5985 8.18849 31.5896L0 31.5527Z'
								fill='white'
							/>
							<path
								d='M32.426 30.7632C30.4806 29.9638 28.6997 28.8038 27.1747 27.3428C25.6725 25.9009 24.4565 24.1813 23.5925 22.2768C22.7206 20.3158 22.2697 18.1898 22.2697 16.0393C22.2697 13.8888 22.7206 11.7628 23.5925 9.80184C24.9306 6.86514 27.0747 4.38012 29.7691 2.64316C32.4635 0.906195 35.5946 -0.00938093 38.7887 0.00562432C41.0741 -0.013882 43.3374 0.459032 45.4285 1.39296C47.5234 2.34443 49.4092 3.71059 50.9749 5.41106L45.4066 10.8387C43.5189 8.77241 41.3129 7.74051 38.7887 7.74297C37.6636 7.73112 36.5478 7.9508 35.509 8.38867C34.5239 8.80612 33.6316 9.41969 32.8852 10.1929C32.1354 10.9636 31.5391 11.873 31.1287 12.8717C30.7052 13.8999 30.4872 15.0028 30.4872 16.1168C30.4872 17.2308 30.7052 18.3337 31.1287 19.3619C31.5448 20.3509 32.1406 21.2521 32.8852 22.0185C33.6316 22.7918 34.5239 23.4053 35.509 23.8228C36.6906 24.3081 37.9633 24.5243 39.2367 24.456C40.5101 24.3878 41.753 24.0368 42.8775 23.428C44.1945 22.6861 45.3138 21.6313 46.1391 20.3544L51.7074 25.7599C50.1283 27.6818 48.1695 29.2478 45.9568 30.3573C43.7275 31.4618 41.2742 32.024 38.7924 31.9992C36.6105 32.0165 34.4468 31.5964 32.426 30.7632Z'
								fill='white'
							/>
							<path
								d='M61.9475 30.7189C58.106 29.1206 55.0241 26.0796 53.3472 22.2325C52.5035 20.2724 52.068 18.1572 52.068 16.019C52.068 13.8808 52.5035 11.7657 53.3472 9.80554C54.6327 6.88618 56.726 4.40645 59.3741 2.66627C62.0221 0.926087 65.1114 0 68.2683 0C71.4252 0 74.5145 0.926087 77.1625 2.66627C79.8106 4.40645 81.9039 6.88618 83.1894 9.80554C84.0331 11.7657 84.4686 13.8808 84.4686 16.019C84.4686 18.1572 84.0331 20.2724 83.1894 22.2325C81.414 26.1485 78.2123 29.2182 74.2536 30.7997C70.2949 32.3812 65.8856 32.3523 61.9475 30.7189ZM65.0305 8.25585C64.0698 8.66734 63.2019 9.27244 62.4795 10.0343C61.7516 10.805 61.184 11.7159 60.8105 12.713C60.0233 14.8437 60.0233 17.1907 60.8105 19.3213C61.1857 20.3175 61.7531 21.2282 62.4795 22.0001C63.2029 22.7608 64.0705 23.3657 65.0305 23.7785C67.1066 24.6393 69.4337 24.6393 71.5098 23.7785C72.4692 23.3648 73.3367 22.76 74.0607 22.0001C74.7848 21.227 75.3508 20.3166 75.7261 19.3213C76.5183 17.1916 76.5183 14.8428 75.7261 12.713C75.3525 11.7169 74.7863 10.8062 74.0607 10.0343C73.3377 9.27322 72.47 8.66825 71.5098 8.25585C69.4347 7.3899 67.1056 7.3899 65.0305 8.25585Z'
								fill='white'
							/>
							<path
								d='M96.1047 23.0515V31.597H87.8433V0.507337H99.2532C100.106 0.507337 101.049 0.538085 102.081 0.59958C103.129 0.663014 104.168 0.824826 105.186 1.08293C106.236 1.35178 107.248 1.75604 108.196 2.28579C109.185 2.84268 110.052 3.59562 110.747 4.49962C111.529 5.53092 112.102 6.70828 112.434 7.96428C112.853 9.6224 112.992 11.34 112.846 13.045C112.693 16.0976 111.6 18.5317 109.566 20.347C107.533 22.1623 104.496 23.0712 100.456 23.0737L96.1047 23.0515ZM104.432 10.1818C104.238 9.67879 103.943 9.22251 103.564 8.84244C103.15 8.43569 102.663 8.11371 102.129 7.89417C101.49 7.63802 100.807 7.51261 100.121 7.5252H96.1958V15.8898H100.933C101.507 15.9029 102.076 15.7843 102.599 15.543C103.056 15.3239 103.461 15.0081 103.787 14.6168C104.111 14.2244 104.351 13.7684 104.494 13.2775C104.646 12.7766 104.723 12.2555 104.723 11.7315C104.725 11.2008 104.626 10.6747 104.432 10.1818Z'
								fill='white'
							/>
							<path
								d='M116.41 0.507337H137.182V7.43665H124.671V12.0119H136.591V19.0224H124.489V24.557H137.32V31.5674H116.41V0.507337Z'
								fill='white'
							/>
							<path
								d='M143.92 31.5011V24.391H152.134C152.78 24.4182 153.424 24.3075 154.025 24.0663C154.428 23.9041 154.799 23.669 155.119 23.3727C155.387 23.0777 155.561 22.7072 155.618 22.31C155.669 21.7838 155.591 21.2528 155.392 20.7641C155.236 20.4076 154.983 20.1031 154.663 19.8859C154.352 19.6826 154.001 19.5481 153.635 19.4911C153.281 19.4329 152.923 19.4021 152.564 19.3988H144.853V12.285H151.744C152.392 12.3129 153.038 12.1944 153.635 11.9382C154.054 11.7544 154.428 11.4784 154.729 11.1302C154.998 10.7996 155.172 10.3999 155.231 9.97528C155.279 9.49318 155.2 9.007 155.002 8.56582C154.835 8.22936 154.583 7.94312 154.273 7.73563C153.962 7.53195 153.612 7.39738 153.245 7.34083C152.891 7.28098 152.533 7.25014 152.174 7.24859H144.55L144.642 0.455792H154.087C155.617 0.412003 157.141 0.654677 158.584 1.1716C159.672 1.57432 160.656 2.22293 161.459 3.06812C162.165 3.83847 162.688 4.76242 162.986 5.769C163.29 6.77208 163.445 7.81543 163.445 8.86469C163.445 10.621 163.073 12.0231 162.327 13.071C161.502 14.1809 160.409 15.0566 159.153 15.6132C160.829 16.2281 162.023 17.0977 162.735 18.2218C163.447 19.346 163.835 20.8219 163.901 22.6495V23.4354C163.887 24.0044 163.81 24.57 163.671 25.1216C163.498 25.8454 163.254 26.5498 162.943 27.2247C162.559 28.0105 162.025 28.7115 161.372 29.2873C160.608 29.9863 159.717 30.5277 158.748 30.8813C157.474 31.3323 156.129 31.5437 154.78 31.5048L143.92 31.5011Z'
								fill='white'
							/>
							<path
								d='M179.192 17.3436C178.857 17.5133 178.521 17.6756 178.186 17.8306C177.854 18.0151 177.548 18.1996 177.275 18.3619C177.001 18.532 176.742 18.7245 176.499 18.9375C176.066 19.2801 175.703 19.7035 175.427 20.1846C175.155 20.697 175.007 21.2672 174.994 21.8487L174.903 24.5274L185.471 24.5754V31.6449H166.714V28.1803C166.714 27.1963 166.723 26.1103 166.74 24.9222C166.757 23.7341 166.794 22.6174 166.853 21.572C166.916 19.9091 167.28 18.5845 167.946 17.5981C168.607 16.619 169.442 15.7731 170.409 15.1039C170.892 14.7652 171.395 14.4559 171.914 14.1778L173.467 13.3476L174.972 12.4473C175.448 12.1612 175.906 11.8445 176.342 11.499C176.82 11.1486 177.211 10.6899 177.483 10.1596C177.684 9.76981 177.755 9.32468 177.687 8.89038C177.611 8.50477 177.4 8.15999 177.093 7.92C176.718 7.63884 176.258 7.49918 175.792 7.5252H167.41V0.507337L175.792 0.551612C176.995 0.5557 178.195 0.679283 179.374 0.920585C180.566 1.14834 181.705 1.60282 182.73 2.25996C183.759 2.93367 184.6 3.86368 185.172 4.96084C185.796 6.11695 186.077 7.6039 186.014 9.4217C186.021 10.3095 185.856 11.1901 185.527 12.0131C185.198 12.8361 184.712 13.5855 184.097 14.2183C183.543 14.7939 182.914 15.2905 182.227 15.6942C181.528 16.1124 180.844 16.49 180.176 16.827C179.855 17.0016 179.527 17.1738 179.192 17.3436Z'
								fill='white'
							/>
							<path d='M191.739 7.29657H186.582V0.548054H200V31.5971L191.739 31.6451V7.29657Z' fill='white' />
						</svg>
						<div className='flex-grow-1'></div>
						<div
							onClick={() => props.setToggleSidebar(!props.toggleSidebar)}
							className='flex-none ml-auto print:hidden'
						>
							<svg width='30' height='30' viewBox='0 0 30 30' fill='none' xmlns='http://www.w3.org/2000/svg'>
								<path
									d='M20 4L10 15L20 26'
									stroke='#F1F5F9'
									strokeWidth='2'
									strokeLinecap='round'
									strokeLinejoin='round'
								/>
							</svg>
						</div>
					</div>

					{orgList.length > 0 && (
						<div className='p-2 mb-3 flex'>
							<select
								className='w-full h-10 rounded border-2 bg-blue_d text-slate-50 border-sky-600'
								value={organizationId}
								onChange={(e) => changeOrganization(e.target.value)}
							>
								<option key='' value='' disabled className='text-slate-100 border-[1px]'>
									-- select --
								</option>
								{orgList.map((m) => (
									<option key={m.id} value={m.organization.id}>
										{m.organization.name}
									</option>
								))}
							</select>
						</div>
					)}
					{/* Display sidebar items */}
					{data.map((link) => {
						return (
							<div key={link.name + "-container"} className='print:hidden'>
								{link.isExpand && link.expandLink && link.expandLink.length ? (
									<>
										<div
											className={"p-2 mb-1 sidebarLink "}
											key={link.name}
											onClick={() => toggleSubLinkContainer(link.name)}
										>
											<span className='flex items-center linkTag' role='button'>
												<span>{link.icon}</span>
												<span className='linkName ml-2'>{link.title}</span>
												<span className='sidebar-link-expand arrow-down ml-auto mr-2' id={link.name + "-arrow"}></span>
											</span>
										</div>
										<div
											className='sub-menu-link-container sub-menu-link-hide'
											id={link.name + "-sub-link"}
											key={link.name + "-sub-link"}
										>
											{link.expandLink.map((subLink) => {
												return (
													<div
														className={"p-1 mb-1 sidebarLink " + (activeComponent === subLink.name ? "active" : "")}
														key={subLink.name + "-sub-link"}
													>
														<Link className='flex items-center linkTag' to={subLink.link}>
															<span className='ml-3'>{subLink.icon}</span>
															<span className='linkName sub-link-name ml-2'>{subLink.title}</span>
														</Link>
													</div>
												)
											})}
										</div>
									</>
								) : (
									<>
										{link.name === "welcome" ? (
											<>
												{!currentOrganization.RegistrationNumber && (
													<div
														className={"p-2 mb-1 sidebarLink" + (activeComponent === link.name ? "active" : "")}
														key={link.name}
													>
														<Link className='flex items-center linkTag' to={link.link}>
															<span>{link.icon}</span>
															<span className='linkName ml-2'>{link.title}</span>
														</Link>
													</div>
												)}
											</>
										) : (
											<div
												className={"p-2 mb-1 sidebarLink" + (activeComponent === link.name ? "active" : "")}
												key={link.name}
											>
												<Link className='flex items-center linkTag' to={link.link}>
													<span>{link.icon}</span>
													<span className='linkName ml-2'>{link.title}</span>
												</Link>
											</div>
										)}
									</>
								)}
							</div>
						)
					})}
					{/* Logout Button */}
					<div className='p-2 mb-1 logout sidebarLink' onClick={logout} key={"logout"}>
						<div className='flex items-center'>
							<span>
								<RiLogoutCircleLine className='linkIcon' />
							</span>
							<span className='linkName ml-2'>{t("Logout")}</span>
						</div>
					</div>
				</div>

				{/* Avatar(User) Detail :- Profile pic, name, email, company name */}
				<div className='w-full flex flex-col items-center'>
					<Link to='/user/profile' className='text-white text-center'>
						{userData?.user?.user_metadata?.avatarurl && userData?.user?.user_metadata?.avatarurl !== "" ? (
							<img
								className='place-self-center object-cover overflow-hidden inline-block h-14 w-14 rounded-full mb-1'
								src={userData?.user?.user_metadata?.avatarurl}
								alt='-'
							></img>
						) : (
							<img
								className='place-self-center object-cover overflow-hidden inline-block h-14 w-14 rounded-full mb-1'
								src={user.imageUrl}
								alt='-'
							></img>
						)}

						{user && (user?.firstName || user?.lastName) ? (
							<div className='text-sky-50'>
								{user?.firstName} {user?.lastName}
							</div>
						) : userData?.user?.user_metadata?.full_name ? (
							<div className='text-sky-50'>{userData?.user?.user_metadata.full_name}</div>
						) : (
							<div>-</div>
						)}
						{userData?.user?.email ? <div className='text-sm text-sky-100'>{userData?.user?.email}</div> : <div>-</div>}
						{userData?.CompanyName ? (
							<div className='text-sm text-sky-100'>{userData?.CompanyName}</div>
						) : currentOrganization && currentOrganization?.CompanyName ? (
							<div className='text-sm text-sky-100'>{currentOrganization?.CompanyName}</div>
						) : (
							<div>-</div>
						)}
					</Link>

					{userData?.RegistrationNumber ? (
						<div className='text-sm'>{userData?.RegistrationNumber}</div>
					) : currentOrganization && currentOrganization?.RegistrationNumber ? (
						<div className='text-sm'>{currentOrganization?.RegistrationNumber}</div>
					) : (
						<div>-</div>
					)}
				</div>
			</div>
		)
	} else {
		// toggled Sidebar
		return (
			<div className='relative h-full w-16 bg-blue_d items-start flex flex-col justify-between print:hidden'>
				<div className='flex flex-col w-100'>
					<div className='logoContainer h-14 mb-4 py-2.5 mx-auto'>
						<svg
							className='-rotate-45'
							width='34'
							height='34'
							viewBox='0 0 34 34'
							fill='none'
							xmlns='http://www.w3.org/2000/svg'
						>
							<g clipPath='url(#clip0)'>
								<path
									d='M28 23.3846C28 22.8718 27.8205 22.4359 27.4615 22.0769L23.4615 18.0769C23.1026 17.7179 22.6667 17.5384 22.1538 17.5384C21.6154 17.5384 21.1538 17.7436 20.7692 18.1538C20.8077 18.1923 20.9295 18.3109 21.1346 18.5096C21.3397 18.7083 21.4776 18.8461 21.5481 18.9231C21.6186 19 21.7147 19.1218 21.8365 19.2884C21.9583 19.4551 22.0417 19.6186 22.0865 19.7788C22.1314 19.9391 22.1538 20.1154 22.1538 20.3077C22.1538 20.8205 21.9743 21.2564 21.6154 21.6154C21.2564 21.9743 20.8205 22.1538 20.3077 22.1538C20.1154 22.1538 19.9391 22.1314 19.7788 22.0865C19.6186 22.0417 19.4551 21.9583 19.2884 21.8365C19.1218 21.7147 19 21.6186 18.9231 21.5481C18.8461 21.4776 18.7083 21.3397 18.5096 21.1346C18.3109 20.9295 18.1923 20.8077 18.1538 20.7692C17.7308 21.1667 17.5192 21.6346 17.5192 22.1731C17.5192 22.6859 17.6987 23.1218 18.0577 23.4808L22.0192 27.4615C22.3654 27.8077 22.8013 27.9808 23.3269 27.9808C23.8397 27.9808 24.2756 27.8141 24.6346 27.4808L27.4615 24.6731C27.8205 24.3141 28 23.8846 28 23.3846ZM14.4808 9.82691C14.4808 9.31409 14.3013 8.87819 13.9423 8.51922L9.98076 4.53845C9.62178 4.17947 9.18588 3.99999 8.67306 3.99999C8.17306 3.99999 7.73717 4.17306 7.36537 4.51922L4.53845 7.32691C4.17947 7.68588 3.99999 8.11537 3.99999 8.61537C3.99999 9.12819 4.17947 9.56409 4.53845 9.92306L8.53845 13.9231C8.8846 14.2692 9.3205 14.4423 9.84614 14.4423C10.3846 14.4423 10.8461 14.2436 11.2308 13.8461C11.1923 13.8077 11.0705 13.6891 10.8654 13.4904C10.6602 13.2917 10.5224 13.1538 10.4519 13.0769C10.3814 13 10.2852 12.8782 10.1634 12.7115C10.0417 12.5449 9.95832 12.3814 9.91345 12.2211C9.86858 12.0609 9.84614 11.8846 9.84614 11.6923C9.84614 11.1795 10.0256 10.7436 10.3846 10.3846C10.7436 10.0256 11.1795 9.84614 11.6923 9.84614C11.8846 9.84614 12.0609 9.86858 12.2211 9.91345C12.3814 9.95832 12.5449 10.0417 12.7115 10.1634C12.8782 10.2852 13 10.3814 13.0769 10.4519C13.1538 10.5224 13.2917 10.6602 13.4904 10.8654C13.6891 11.0705 13.8077 11.1923 13.8461 11.2308C14.2692 10.8333 14.4808 10.3654 14.4808 9.82691ZM31.6923 23.3846C31.6923 24.9231 31.1474 26.2243 30.0577 27.2884L27.2308 30.0961C26.1667 31.1602 24.8654 31.6923 23.3269 31.6923C21.7756 31.6923 20.4679 31.1474 19.4038 30.0577L15.4423 26.0769C14.3782 25.0128 13.8461 23.7115 13.8461 22.1731C13.8461 20.5961 14.4102 19.2564 15.5384 18.1538L13.8461 16.4615C12.7436 17.5897 11.4102 18.1538 9.84614 18.1538C8.30768 18.1538 6.99999 17.6154 5.92306 16.5384L1.92306 12.5384C0.84614 11.4615 0.307678 10.1538 0.307678 8.61537C0.307678 7.07691 0.85255 5.77563 1.94229 4.71152L4.76922 1.90383C5.83332 0.83973 7.1346 0.307678 8.67306 0.307678C10.2243 0.307678 11.532 0.85255 12.5961 1.94229L16.5577 5.92306C17.6218 6.98717 18.1538 8.28845 18.1538 9.82691C18.1538 11.4038 17.5897 12.7436 16.4615 13.8461L18.1538 15.5384C19.2564 14.4102 20.5897 13.8461 22.1538 13.8461C23.6923 13.8461 25 14.3846 26.0769 15.4615L30.0769 19.4615C31.1538 20.5384 31.6923 21.8461 31.6923 23.3846Z'
									fill='white'
								/>
							</g>
							<defs>
								<clipPath id='clip0'>
									<rect width='32' height='32' fill='white' />
								</clipPath>
							</defs>
						</svg>
					</div>

					{data.map((link) => {
						return (
							<div key={link.name + "-toggle-container"} className='side-menu-link-toggle-container'>
								{link.isExpand && link.expandLink && link.expandLink.length ? (
									<div className='transaction-link' key={link.name + "-toggle"}>
										<div
											className={
												"p-2 mb-1 grid justify-items-center sidebarLink" +
												(activeComponent === link.name ? "active" : "")
											}
										>
											<Link to={link.link}>
												<span>{link.icon}</span>
											</Link>
										</div>
										<div
											className='side-menu-link-toggle-container border-0 rounded-r-md transaction-sub-link'
											key={link.name + "-sub-link-toggle"}
										>
											{link.expandLink.map((subLink) => {
												return (
													<div
														className={"p-1 mb-1 sidebarLink " + (activeComponent === subLink.name ? "active" : "")}
														key={subLink.name + "-sub-link-toggle"}
													>
														<Link className='flex items-center z-50 linkTag' to={subLink.link}>
															<span className='ml-2'>{subLink.icon}</span>
															<span className='linkName sub-link-name ml-2'>{subLink.title}</span>
														</Link>
													</div>
												)
											})}
										</div>
									</div>
								) : (
									<>
										{link.name === "welcome" ? (
											<>
												{!currentOrganization?.RegistrationNumber && (
													<div
														className={
															"p-2 mb-1 grid justify-center sidebarLink " +
															(activeComponent === link.name ? "active" : "")
														}
														key={link.name + "-toggle"}
													>
														<Link to={link.link}>
															<span>{link.icon}</span>
														</Link>
													</div>
												)}
											</>
										) : (
											<div
												className={
													"p-2 mb-1 grid justify-center sidebarLink " + (activeComponent === link.name ? "active" : "")
												}
												key={link.name + "-toggle"}
											>
												<Link to={link.link}>
													<span>{link.icon}</span>
												</Link>
											</div>
										)}
									</>
								)}
							</div>
						)
					})}
					<div className='p-2 mb-1 grid justify-items-center logout sidebarLink' onClick={logout} key='logout-toggle'>
						<div>
							<span>
								<RiLogoutCircleLine className='linkIcon' />
							</span>
						</div>
					</div>
				</div>
				<div className='w-full flex flex-col items-center'>
					<Link to='/user/profile' className='text-white w-full px-1'>
						<div className='grid'>
							{userData?.user?.user_metadata?.avatarurl ? (
								<img
									className='place-self-center object-cover overflow-hidden inline-block h-12 w-12 rounded-full mb-1'
									src={userData?.user?.user_metadata?.avatarurl}
									alt='-'
								></img>
							) : (
								<img
									className='place-self-center object-cover overflow-hidden inline-block h-12 w-12 rounded-full mb-1'
									src={user.imageUrl}
									alt='-'
								></img>
							)}

							{user && (user?.firstName || user?.lastName) ? (
								<div className='text-sm text-sky-100 truncate'>
									{user?.firstName} {user?.lastName}
								</div>
							) : userData?.user?.user_metadata?.full_name ? (
								<div className='text-sm text-sky-100 truncate'>{userData?.user?.user_metadata.full_name}</div>
							) : (
								<div className='place-self-center'>-</div>
							)}
						</div>
						{currentOrganization?.email ? (
							<div className='text-sm text-sky-100 truncate'>{currentOrganization?.email}</div>
						) : (
							<div>-</div>
						)}
						{userData?.CompanyName ? (
							<div className='text-sm text-sky-100 truncate'>{userData?.CompanyName}</div>
						) : currentOrganization && currentOrganization?.CompanyName ? (
							<div className='text-sm text-sky-100 truncate'>{currentOrganization?.CompanyName}</div>
						) : (
							<div>-</div>
						)}
					</Link>

					{userData?.RegistrationNumber ? (
						<div className='px-1 w-full text-sm text-slate-900 truncate'>{userData?.RegistrationNumber}</div>
					) : currentOrganization && currentOrganization?.RegistrationNumber ? (
						<div className='px-1 w-full text-sm text-slate-900 truncate'>{currentOrganization?.RegistrationNumber}</div>
					) : (
						<div>-</div>
					)}
				</div>
			</div>
		)
	}
}

export default Sidebar
