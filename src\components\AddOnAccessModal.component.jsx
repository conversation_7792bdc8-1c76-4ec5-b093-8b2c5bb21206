/* eslint-disable import/no-unresolved */
import { Dialog, Transition } from "@headlessui/react"
import axios from "axios"
import { Fragment, useState } from "react"
import { useTranslation } from "react-i18next"
import { useLocation, useNavigate } from "react-router-dom"

import { useToast } from "../hooks"

import Button from "./ui/Button"
import FormInput from "./ui/FormInput"

export default function AddOnAccessModal({ title }) {
	//const [open, setOpen] = useState(true)

	const Toast = useToast()
	const [email, setEmail] = useState()

	const [phoneNumber, setPhoneNumber] = useState()

	const [userName, setUserName] = useState()

	const [t] = useTranslation("common")

	const location = useLocation()

	//const open = useSelector((state) => state.reportingEntity)

	const navigate = useNavigate()

	const handleWantToGetAccess = async (e) => {
		try {
			e.preventDefault()
			const myForm = e.target
			const formData = new FormData(myForm)

			await axios.post("/", formData, {
				headers: { "Content-Type": "multipart/form-data" },
			})

			setUserName("")
			setEmail("")
			setPhoneNumber("")

			// Close the modal
			Toast("success", "Thanks we will connect with you as soon as possible")

			navigate(-1)
		} catch (error) {
			console.log(error)
		}
	}

	return (
		<div className='z-50'>
			<Transition.Root show={true} as={Fragment}>
				<Dialog as='div' className='relative z-50' onClose={() => navigate(location.state.from)}>
					<Transition.Child
						as={Fragment}
						enter='ease-out duration-300'
						enterFrom='opacity-0'
						enterTo='opacity-100'
						leave='ease-in duration-200'
						leaveFrom='opacity-100'
						leaveTo='opacity-0'
					>
						<div className='fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity' />
					</Transition.Child>

					<div className='fixed inset-0 z-50 overflow-y-auto'>
						<div className='flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0'>
							<Transition.Child
								as={Fragment}
								enter='ease-out duration-300'
								enterFrom='opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95'
								enterTo='opacity-100 translate-y-0 sm:scale-100'
								leave='ease-in duration-200'
								leaveFrom='opacity-100 translate-y-0 sm:scale-100'
								leaveTo='opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95'
							>
								<Dialog.Panel className='relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6'>
									<div className='flex align-middle '>
										<svg
											xmlns='http://www.w3.org/2000/svg'
											fill='none'
											viewBox='0 0 24 24'
											strokeWidth={1.5}
											stroke='#f85656'
											className='w-12 h-12'
										>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												d='M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z'
											/>
										</svg>
										<Dialog.Title as='h2' className='text-lg font-semibold mt-0 p-0 leading-6 ml-3 text-red-500'>
											{title}
										</Dialog.Title>
									</div>

									<form name='addOnAccess' onSubmit={handleWantToGetAccess}>
										<input type='hidden' name='form-name' value='subscribe' />
										<div className='form-group mt-8'>
											<FormInput
												title={t("common:Name")}
												name='name'
												value={userName}
												type={"text"}
												required
												handleChange={(e) => setUserName(e.target.value)}
											/>
										</div>
										<div className='form-group mt-2'>
											<FormInput
												title={t("common:Email")}
												name='email'
												value={email}
												type={"email"}
												required
												handleChange={(e) => setEmail(e.target.value)}
											/>
										</div>
										<div className='form-group mt-2'>
											<FormInput
												title={t("common:Telephone")}
												name='phonenumber'
												value={phoneNumber}
												type={"text"}
												required
												handleChange={(e) => setPhoneNumber(e.target.value)}
											/>
										</div>

										<div className='w-full mt-6 flex justify-center'>
											<div className='mr-4'>
												<Button
													title={t("Cancel")}
													handleClick={() => {
														console.log(location)
														navigate(-1)
													}}
													type='button'
													variation='danger'
													name='cancel'
												/>
											</div>
											<Button variation='secondary' title={t("Give me Access")} type='submit' name='submit' />
										</div>
									</form>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition.Root>
		</div>
	)
}
