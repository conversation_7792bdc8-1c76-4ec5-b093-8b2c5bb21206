# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

/dist
# misc
.env
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.aider*
mightymeld.secrets

npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
src/components/dashboard/SCOPE2_CALCULATION_METHODS.unused.js

# Local Netlify folder
.netlify

.yarn

/config