import { useQuery } from "@tanstack/react-query"

export default function useGetCO2AddedData(app, input, enabled = true) {
	return useQuery({
		queryKey: ["CO2AddedData", input],
		queryFn: async () => {
			const { RegistrationNumber, year, AnalysisIDs } = input
			if (RegistrationNumber !== "") return await app.dashboardCO2AddedData(RegistrationNumber, year, AnalysisIDs)
		},

		enabled: input !== undefined && enabled,
		refetchOnMount: true,
		retry: 5,
	})
}
