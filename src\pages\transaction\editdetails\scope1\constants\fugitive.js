export const METRIC_LIST_FUGITIVE = [
	{ unit: "kg", value: "Carbondioxide", label: "Carbondioxide" },
	{ unit: "kg", value: "Methane", label: "Methane" },
	{ unit: "kg", value: "Nitrousoxide", label: "Nitrousoxide" },
	{ unit: "kg", value: "HFC-23", label: "HFC-23" },
	{ unit: "kg", value: "HFC-32", label: "HFC-32" },
	{ unit: "kg", value: "HFC-41", label: "HFC-41" },
	{ unit: "kg", value: "HFC-125", label: "HFC-125" },
	{ unit: "kg", value: "HFC-134", label: "HFC-134" },
	{ unit: "kg", value: "HFC-134a", label: "HFC-134a" },
	{ unit: "kg", value: "HFC-143", label: "HFC-143" },
	{ unit: "kg", value: "HFC-143a", label: "HFC-143a" },
	{ unit: "kg", value: "HFC-152a", label: "HFC-152a" },
	{ unit: "kg", value: "HFC-227ea", label: "HFC-227ea" },
	{ unit: "kg", value: "HFC-236fa", label: "HFC-236fa" },
	{ unit: "kg", value: "HFC-245fa", label: "HFC-245fa" },
	{ unit: "kg", value: "HFC-43-I0mee", label: "HFC-43-I0mee" },
	{ unit: "kg", value: "Perfluoromethane(PFC-14)", label: "Perfluoromethane(PFC-14)" },
	{ unit: "kg", value: "Perfluoroethane(PFC-116)", label: "Perfluoroethane(PFC-116)" },
	{ unit: "kg", value: "Perfluoropropane(PFC-218)", label: "Perfluoropropane(PFC-218)" },
	{ unit: "kg", value: "Perfluorocyclobutane(PFC-318)", label: "Perfluorocyclobutane(PFC-318)" },
	{ unit: "kg", value: "Perfluorobutane(PFC-3-1-10)", label: "Perfluorobutane(PFC-3-1-10)" },
	{ unit: "kg", value: "Perfluoropentane(PFC-4-1-12)", label: "Perfluoropentane(PFC-4-1-12)" },
	{ unit: "kg", value: "Perfluorohexane(PFC-5-1-14)", label: "Perfluorohexane(PFC-5-1-14)" },
	{ unit: "kg", value: "Sulphur hexafluoride (SF6)", label: "Sulphur hexafluoride (SF6)" },
	{ unit: "kg", value: "HFC-152", label: "HFC-152" },
	{ unit: "kg", value: "HFC-161", label: "HFC-161" },
	{ unit: "kg", value: "HFC-236cb", label: "HFC-236cb" },
	{ unit: "kg", value: "HFC-236ea", label: "HFC-236ea" },
	{ unit: "kg", value: "HFC-245ca", label: "HFC-245ca" },
	{ unit: "kg", value: "HFC-365mfc", label: "HFC-365mfc" },
	{ unit: "kg", value: "R404A", label: "R404A" },
	{ unit: "kg", value: "R407A", label: "R407A" },
	{ unit: "kg", value: "R407C", label: "R407C" },
	{ unit: "kg", value: "R407F", label: "R407F" },
	{ unit: "kg", value: "R408A", label: "R408A" },
	{ unit: "kg", value: "R410A", label: "R410A" },
	{ unit: "kg", value: "R507A", label: "R507A" },
	{ unit: "kg", value: "R508B", label: "R508B" },
	{ unit: "kg", value: "R-403a", label: "R-403a" },
	{ unit: "kg", value: "CFC-11/R11=trichlorofluoromethane", label: "CFC-11/R11=trichlorofluoromethane" },
	{ unit: "kg", value: "CFC-12/R12=dichlorodifluoromethane", label: "CFC-12/R12=dichlorodifluoromethane" },
	{ unit: "kg", value: "CFC-13", label: "CFC-13" },
	{ unit: "kg", value: "CFC-113", label: "CFC-113" },
	{ unit: "kg", value: "CFC-114", label: "CFC-114" },
	{ unit: "kg", value: "CFC-115", label: "CFC-115" },
	{ unit: "kg", value: "Halon-1211", label: "Halon-1211" },
	{ unit: "kg", value: "Halon-1301", label: "Halon-1301" },
	{ unit: "kg", value: "Halon-2402", label: "Halon-2402" },
	{ unit: "kg", value: "Carbontetrachloride", label: "Carbontetrachloride" },
	{ unit: "kg", value: "Methylbromide", label: "Methylbromide" },
	{ unit: "kg", value: "Methylchloroform", label: "Methylchloroform" },
	{ unit: "kg", value: "HCFC-22/R22=chlorodifluoromethane", label: "HCFC-22/R22=chlorodifluoromethane" },
	{ unit: "kg", value: "HCFC-123", label: "HCFC-123" },
	{ unit: "kg", value: "HCFC-124", label: "HCFC-124" },
	{ unit: "kg", value: "HCFC-141b", label: "HCFC-141b" },
	{ unit: "kg", value: "HCFC-142b", label: "HCFC-142b" },
	{ unit: "kg", value: "HCFC-225ca", label: "HCFC-225ca" },
	{ unit: "kg", value: "HCFC-225cb", label: "HCFC-225cb" },
	{ unit: "kg", value: "HCFC-21", label: "HCFC-21" },
	{ unit: "kg", value: "Nitrogentrifluoride", label: "Nitrogentrifluoride" },
	{ unit: "kg", value: "PFC-9-1-18", label: "PFC-9-1-18" },
	{ unit: "kg", value: "Trifluoromethylsulphurpentafluoride", label: "Trifluoromethylsulphurpentafluoride" },
	{ unit: "kg", value: "Perfluorocyclopropane", label: "Perfluorocyclopropane" },
	{ unit: "kg", value: "HFE-125", label: "HFE-125" },
	{ unit: "kg", value: "HFE-134", label: "HFE-134" },
	{ unit: "kg", value: "HFE-143a", label: "HFE-143a" },
	{ unit: "kg", value: "HCFE-235da2", label: "HCFE-235da2" },
	{ unit: "kg", value: "HFE-245cb2", label: "HFE-245cb2" },
	{ unit: "kg", value: "HFE-245fa2", label: "HFE-245fa2" },
	{ unit: "kg", value: "HFE-254cb2", label: "HFE-254cb2" },
	{ unit: "kg", value: "HFE-347mcc3", label: "HFE-347mcc3" },
	{ unit: "kg", value: "HFE-347pcf2", label: "HFE-347pcf2" },
	{ unit: "kg", value: "HFE-356pcc3", label: "HFE-356pcc3" },
	{ unit: "kg", value: "HFE-449sl(HFE-7100)", label: "HFE-449sl(HFE-7100)" },
	{ unit: "kg", value: "HFE-569sf2(HFE-7200)", label: "HFE-569sf2(HFE-7200)" },
	{ unit: "kg", value: "HFE-43-10pccc124(H-Galden1040x)", label: "HFE-43-10pccc124(H-Galden1040x)" },
	{ unit: "kg", value: "HFE-236ca12(HG-10)", label: "HFE-236ca12(HG-10)" },
	{ unit: "kg", value: "HFE-338pcc13(HG-01)", label: "HFE-338pcc13(HG-01)" },
	{ unit: "kg", value: "PFPMIE", label: "PFPMIE" },
	{ unit: "kg", value: "Dimethylether", label: "Dimethylether" },
	{ unit: "kg", value: "Methylenechloride", label: "Methylenechloride" },
	{ unit: "kg", value: "Methylchloride", label: "Methylchloride" },
	{ unit: "kg", value: "R290=propane", label: "R290=propane" },
	{ unit: "kg", value: "R600A=isobutane", label: "R600A=isobutane" },
	{ unit: "kg", value: "R1234yf", label: "R1234yf" },
	{ unit: "kg", value: "R1234ze", label: "R1234ze" },
	{ unit: "kg", value: "R406A", label: "R406A" },
	{ unit: "kg", value: "R409A", label: "R409A" },
	{ unit: "kg", value: "R502", label: "R502" },
]

export const FUGITIVE_GASES_DATA = [
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Carbondioxide",
		co2e: 1,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Methane",
		co2e: 25,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Nitrousoxide",
		co2e: 298,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-23",
		co2e: 14800,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-32",
		co2e: 675,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-41",
		co2e: 92,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-125",
		co2e: 3500,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-134",
		co2e: 1100,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-134a",
		co2e: 1430,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-143",
		co2e: 353,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-143a",
		co2e: 4470,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-152a",
		co2e: 124,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-227ea",
		co2e: 3220,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-236fa",
		co2e: 9810,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-245fa",
		co2e: 1030,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-43-I0mee",
		co2e: 1640,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluoromethane(PFC-14)",
		co2e: 7390,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluoroethane(PFC-116)",
		co2e: 12200,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluoropropane(PFC-218)",
		co2e: 8830,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluorocyclobutane(PFC-318)",
		co2e: 10300,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluorobutane(PFC-3-1-10)",
		co2e: 8860,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluoropentane(PFC-4-1-12)",
		co2e: 9160,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluorohexane(PFC-5-1-14)",
		co2e: 9300,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Sulphur hexafluoride (SF6)",
		co2e: 22800,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-152",
		co2e: 53,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-161",
		co2e: 12,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-236cb",
		co2e: 1340,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-236ea",
		co2e: 1370,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-245ca",
		co2e: 693,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFC-365mfc",
		co2e: 794,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R404A",
		co2e: 3922,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R407A",
		co2e: 2107,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R407C",
		co2e: 1774,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R407F",
		co2e: 1825,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R408A",
		co2e: 3152,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R410A",
		co2e: 2088,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R507A",
		co2e: 3985,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R508B",
		co2e: 13396,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R-403a",
		co2e: 3124,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-11/R11=trichlorofluoromethane",
		co2e: 4750,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-12/R12=dichlorodifluoromethane",
		co2e: 10900,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-13",
		co2e: 14400,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-113",
		co2e: 6130,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-114",
		co2e: 10000,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "CFC-115",
		co2e: 7370,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Halon-1211",
		co2e: 1890,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Halon-1301",
		co2e: 7140,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Halon-2402",
		co2e: 1640,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Carbontetrachloride",
		co2e: 1400,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Methylbromide",
		co2e: 5,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Methylchloroform",
		co2e: 146,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-22/R22=chlorodifluoromethane",
		co2e: 1810,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-123",
		co2e: 77,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-124",
		co2e: 609,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-141b",
		co2e: 725,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-142b",
		co2e: 2310,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-225ca",
		co2e: 122,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-225cb",
		co2e: 595,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFC-21",
		co2e: 151,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Nitrogentrifluoride",
		co2e: 17200,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "PFC-9-1-18",
		co2e: 7500,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Trifluoromethylsulphurpentafluoride",
		co2e: 17700,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Perfluorocyclopropane",
		co2e: 17340,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-125",
		co2e: 14900,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-134",
		co2e: 6320,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-143a",
		co2e: 756,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HCFE-235da2",
		co2e: 350,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-245cb2",
		co2e: 708,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-245fa2",
		co2e: 659,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-254cb2",
		co2e: 359,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-347mcc3",
		co2e: 575,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-347pcf2",
		co2e: 580,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-356pcc3",
		co2e: 110,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-449sl(HFE-7100)",
		co2e: 297,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-569sf2(HFE-7200)",
		co2e: 59,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-43-10pccc124(H-Galden1040x)",
		co2e: 1870,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-236ca12(HG-10)",
		co2e: 2800,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "HFE-338pcc13(HG-01)",
		co2e: 1500,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "PFPMIE",
		co2e: 10300,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Dimethylether",
		co2e: 1,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Methylenechloride",
		co2e: 8.7,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "Methylchloride",
		co2e: 13,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R290=propane",
		co2e: 3.3,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R600A=isobutane",
		co2e: 3,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R1234yf",
		co2e: 4,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R1234ze",
		co2e: 6,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R406A",
		co2e: 1943,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R409A",
		co2e: 1585,
	},
	{
		type: "refrigerantLosses",
		weight: 1000,
		unit: "kg",
		gasType: "R502",
		co2e: 4657,
	},
]
