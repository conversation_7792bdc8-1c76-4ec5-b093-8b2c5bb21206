/* eslint-disable no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react"
import { Navbar } from "react-bootstrap"
import * as AiIcons from "react-icons/ai"
import { FaBars } from "react-icons/fa"
import "../../styles/header.css"
import { useTranslation } from "react-i18next"
import { FeedbackFish } from "@feedback-fish/react"
import { useDispatch, useSelector } from "react-redux"
import { Link } from "react-router-dom"
import Select from "react-select"

import { useToast } from "../../hooks"
import { useRealmApp } from "../../realm/RealmAppProvider"
import { handleSetReportingYear, handleWorkingOnData, currentOrganizationAction } from "../../store/actions/UserAction"

import { START_YEAR, ERROR_STATUS_CODE, PROGRESS_THRESHOLD_1, PROGRESS_THRESHOLD_2, DELAY_TIME } from "./constants"
import WorkingOnDataIndicator from "./WorkingOnDataIndicator"

import FileIssueModal from "@/components/FileIssueModal.component"

const Header = (props) => {
	const { t, i18n } = useTranslation("header")
	const [email, setEmail] = useState("")
	const currentYearR = useSelector((state) => state.reporting)
	const currentOrganization = useSelector((state) => state.user.currentOrganization)
	const workingOnData = useSelector((state) => state.uploading)
	const RegistrationNumber = currentOrganization ? currentOrganization.RegistrationNumber : ""
	const [progressState, setProgressState] = useState(1)
	const [open, setOpen] = useState(false)
	const Toast = useToast()
	const [errorMessage, setErrorMessage] = useState("")

	const [currentLocale, setCurrentLocale] = useState("")
	const dispatch = useDispatch()
	const [reportingYear, setReportingYears] = useState([
		{ label: "2020", value: "2020" },
		{ label: "2021", value: "2021" },
		{ label: "2022", value: "2022" },
	])
	const [currentReportingYear, setCurrentReportingYear] = useState({ label: currentYearR, value: currentYearR })
	const app = useRealmApp()
	const changeLanguage = (lang) => {
		i18n.changeLanguage(lang)
		localStorage.setItem("userLanguage", lang)
		setCurrentLocale(lang)
	}

	const getCurrentYear = () => {
		const date = new Date()
		let currentYear = date.getFullYear()
		let data = []
		for (let i = START_YEAR; i <= currentYear; i++) {
			data.push({ label: `${i}`, value: `${i}` })
		}
		//let data = { label: `${currentYear}`, value: `${currentYear}` }
		let newData = [...new Map(data.map((item) => [item["value"], item])).values()]
		setReportingYears(newData)
	}

	const selectYear = (e) => {
		setCurrentReportingYear(e)
		dispatch(handleSetReportingYear(e.value))
	}

	const getCompanyData = async () => {
		try {
			const realmUser = await app.getRealmUserData(currentOrganization?.netlifyID)
			//let orgCreated = !realmUser.organizationId ? false : true
			//dispatch(UserAction({ ...realmUser, organizationCreated: orgCreated }))
			dispatch(
				currentOrganizationAction({
					CompanyName: realmUser.CompanyName,
					RegistrationNumber: realmUser.RegistrationNumber,
					company: realmUser.companyInfo,
					email: realmUser.email,
					name: realmUser.name,
					netlifyID: realmUser.netlifyID,
					organizationId: realmUser.organizationId,
					Subscription: realmUser.Subscription,
					//organizationCreated: orgCreated,
				})
			)
		} catch (error) {}
	}

	useEffect(() => {
		//let intervalId

		const fetchData = async () => {
			try {
				//if (RegistrationNumber) {
				let isRequestCompleted = false

				setProgressState(1)

				//await delay(2000)

				while (!isRequestCompleted) {
					const data = await app.getTriggerProgress(RegistrationNumber, currentOrganization.netlifyID)
					// if (data.error) {
					// 	dispatch(handleWorkingOnData(false))
					// 	isRequestCompleted = true
					// 	return
					// }
					if (!data?.success && data?.statusCode === ERROR_STATUS_CODE) {
						dispatch(handleWorkingOnData(false))
						setErrorMessage(data?.message || "")
						//Toast("error", `we might have some issue in file possible reason ${}`)
						setOpen(true)
						isRequestCompleted = true
					}

					if (data.percentage >= PROGRESS_THRESHOLD_1 && data.percentage < PROGRESS_THRESHOLD_2) {
						setProgressState(2)
					}
					if (data.percentage > PROGRESS_THRESHOLD_2) {
						setProgressState(3)
					}

					if (data.isCompleted) {
						dispatch(handleWorkingOnData(false))
						getCompanyData()
						Toast("success", "Thanks for your patience now you can continue")
						isRequestCompleted = true
					} else {
						await delay(DELAY_TIME)
					}
				}
				//}
			} catch (error) {
				Toast("error", "Sorry! we are facing some issues.")
			}
		}

		if (workingOnData && RegistrationNumber !== "") fetchData()
	}, [RegistrationNumber, workingOnData])

	const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

	useEffect(() => {
		setCurrentLocale(i18n.language)
		const userEmail = localStorage.getItem("email")
		if (userEmail) {
			setEmail(userEmail)
		}
		getCurrentYear()
	}, [])

	return (
		<Navbar expand='lg' className='headerClass print:hidden'>
			<FileIssueModal
				open={open}
				setOpen={setOpen}
				userID={currentOrganization?.netlifyID}
				RegistrationNumber={currentOrganization?.RegistrationNumber || "N/A"}
				userEmail={currentOrganization?.email || ""}
				errorMessage={errorMessage}
			/>

			<div className='flex flex-row w-full h-full items-center'>
				<div>
					{/* Toggle sidebar button */}
					{props.toggleSidebar ? (
						<></>
					) : (
						<FaBars onClick={() => props.setToggleSidebar(!props.toggleSidebar)} className='ml-2 cursor-pointer' />
					)}
				</div>

				<div className='flex flex-row w-[100%]  justify-between'>
					<div className='flex flex-row items-center z-40'>
						<label className='metric-label ml-6 mb-0 text-xl' htmlFor={"reportingYear1"}>
							{t("Reporting year")}
						</label>
						<Select
							className='w-48 px-4'
							id={"reportingYear1"}
							value={currentReportingYear}
							onChange={(e) => {
								//(e)
								selectYear(e)
							}}
							options={reportingYear}
						/>
					</div>

					<div className='flex justify-between items-center'>
						{/* <div className="hidden">{t("Company")}</div> */}

						{workingOnData && (
							<div className='ml-3'>
								<WorkingOnDataIndicator state={progressState} />
							</div>
						)}

						<div className='flex flex-row gap-2 ml-3'>
							<FeedbackFish projectId='388006e9d274cd' userId={email}>
								<div className='flex flex-row gap-2 feedbackDiv'>
									<svg width='20' height='20' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
										<path
											d='M11.5058 18.8758L10.5065 20L3.01191 11.6494C2.51757 11.1083 2.12819 10.4578 1.86828 9.73903C1.60838 9.02024 1.48357 8.2487 1.50173 7.47298C1.5199 6.69726 1.68063 5.93418 1.97381 5.23178C2.267 4.52938 2.68628 3.90289 3.20525 3.39175C3.72423 2.8806 4.33166 2.49588 4.98929 2.26181C5.64692 2.02775 6.34051 1.9494 7.02637 2.03171C7.71225 2.11403 8.37554 2.3552 8.97448 2.74007C9.57342 3.12494 10.095 3.64516 10.5065 4.26797C11.3015 3.07534 12.4852 2.2869 13.797 2.07606C15.1088 1.86523 16.4413 2.2493 17.5014 3.14377C18.5616 4.03824 19.2624 5.36984 19.4497 6.84563C19.6372 8.32143 19.2958 9.82054 18.5007 11.0132'
											stroke='#E11D48'
											strokeWidth='2'
											strokeLinecap='round'
											strokeLinejoin='round'
										/>
										<path
											d='M18.2006 20.8175L16.0286 21.9555C15.9642 21.989 15.8917 22.004 15.8192 21.9987C15.7467 21.9934 15.6771 21.9681 15.6182 21.9256C15.5593 21.8831 15.5134 21.825 15.4855 21.7579C15.4577 21.6907 15.4491 21.6172 15.4606 21.5455L15.8756 19.1345L14.1186 17.4275C14.0662 17.3768 14.0291 17.3123 14.0115 17.2416C13.9939 17.1708 13.9966 17.0965 14.0192 17.0271C14.0418 16.9578 14.0835 16.8962 14.1394 16.8494C14.1954 16.8026 14.2634 16.7725 14.3356 16.7625L16.7636 16.4105L17.8496 14.2175C17.8821 14.1521 17.9322 14.0972 17.9942 14.0588C18.0562 14.0204 18.1277 14 18.2006 14C18.2736 14 18.3451 14.0204 18.4071 14.0588C18.4691 14.0972 18.5191 14.1521 18.5516 14.2175L19.6376 16.4105L22.0656 16.7625C22.1377 16.7728 22.2054 16.8031 22.2611 16.85C22.3168 16.8968 22.3583 16.9583 22.3809 17.0275C22.4034 17.0967 22.4062 17.1708 22.3888 17.2415C22.3715 17.3122 22.3347 17.3766 22.2826 17.4275L20.5256 19.1345L20.9396 21.5445C20.9521 21.6163 20.9441 21.6902 20.9166 21.7578C20.8891 21.8254 20.8433 21.8839 20.7842 21.9267C20.7252 21.9695 20.6553 21.9949 20.5825 22C20.5098 22.005 20.4371 21.9896 20.3726 21.9555L18.2006 20.8175Z'
											stroke='#F59E0B'
											strokeWidth='1.5'
											strokeMiterlimit='16'
											strokeLinecap='round'
											strokeLinejoin='round'
										/>
									</svg>
									<div className='font-medium'>{t("Feedback")}</div>
								</div>
							</FeedbackFish>
						</div>
						<div className='ml-3'> | </div>
						<div className='ml-2 font-medium'>
							<button onClick={() => window.open("https://docs.scope321.com", "_blank", "noopener noreferrer")}>
								{t("Docs")}
							</button>
						</div>
						<div className='ml-3'> | </div>
						<div className='ml-2 hidden'>{t("Support")}</div>
						<div className='ml-2 hidden'>
							<input type='text' className='searchInput'></input>
						</div>

						<div className='flex items-center mx-3'>
							<AiIcons.AiOutlineGlobal />
							{/* eslint-disable-next-line */}
							<a
								onClick={() => changeLanguage("en")}
								className='language-link mx-1'
								style={{
									fontWeight: currentLocale === "en" ? `700` : ``,
									textDecoration: currentLocale === "en" ? `underline` : `none`,
								}}
							>
								en
							</a>
							|{/* eslint-disable-next-line */}
							<a
								onClick={() => changeLanguage("no")}
								className='language-link mx-1'
								style={{
									fontWeight: currentLocale === "no" ? `700` : ``,
									textDecoration: currentLocale === "no" ? `underline` : `none`,
								}}
							>
								no
							</a>
						</div>
					</div>
				</div>
			</div>
		</Navbar>
	)
}

export default Header
