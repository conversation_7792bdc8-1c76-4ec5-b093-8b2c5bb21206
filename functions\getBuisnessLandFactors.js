exports = async function(args) {
  const { Level2, Level3, feulType } = args; // Get input arguments

  const db = context.services.get("mongodb-atlas").db("factors"); // Database Name
  const collection = db.collection("beis"); // Collection Name

  let matchStage = {
    $or: [
      { "Level 1": "Business travel- land" },
      { "Level 1": "Business travel- sea" }
    ]
  };

  if (Level2) matchStage["Level 2"] = Level2;
  if (Level3) matchStage["Level 3"] = Level3;
  if (feulType && Level2!=="Motorbike") matchStage["Column Text"] = feulType;

  let projectionField = "Level 2"; // Default projection

  if (Level2 && !Level3) projectionField = "Level 3";
  else if (Level2 && Level3 && !feulType && Level2!=="Motorbike") projectionField = "Column Text";
  else if (Level2 && Level3 && (feulType || Level2==="Motorbike")) projectionField = "GHG Conversion Factor";

  const result = await collection.aggregate([
    { $match: matchStage }, // Filter based on provided parameters
    { $group: { _id: `$${projectionField}` } }, // Get unique values
    { $sort: { _id: 1 } } // Sort alphabetically
  ]).toArray();

  return result.map(item => item._id).filter(value => value !== null && value !== undefined);
};
