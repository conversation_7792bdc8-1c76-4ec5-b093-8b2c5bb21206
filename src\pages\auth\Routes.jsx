import React from "react"
import { Route, Routes } from "react-router-dom"

// routes config
const Main = React.lazy(() => import("../Main"))
const Welcome = React.lazy(() => import("../welcome/Welcome"))
const Suppliers = React.lazy(() => import("../supplier/Suppliers"))
const Dashboard = React.lazy(() => import("../dashboard/Dashboard"))
const Organization = React.lazy(() => import("../organization/Organization"))
const UsersProfile = React.lazy(() => import("../userProfile/UsersProfile"))
const Transactions = React.lazy(() => import("../transaction/Transactions"))
const Report = React.lazy(() => import("../report/Report"))
const ProjectReport = React.lazy(() => import("../project/ProjectReport"))
//const Settings = React.lazy(() => import("../settings/Settings"))

const routes = [
	{ path: "/", exact: true, name: "Main", component: Main },
	{ path: "/welcome", exact: true, name: "Welcome", component: Welcome },
	{ path: "/dashboard", exact: true, name: "Dashboard", component: Dashboard },
	{
		path: "/organization",
		exact: true,
		name: "Organization",
		component: Organization,
	},
	{
		path: "/user/profile",
		exact: true,
		name: "UsersProfile",
		component: UsersProfile,
	},
	{ path: "/suppliers", exact: true, name: "supplier", component: Suppliers },
	{
		path: "/transactions",
		exact: true,
		name: "transaction",
		component: Transactions,
	},
	{
		path: "/transactions/scope1",
		exact: true,
		name: "transaction",
		component: Transactions,
	},
	{
		path: "/transactions/scope2",
		exact: true,
		name: "transaction",
		component: Transactions,
	},
	{
		path: "/transactions/scope3",
		exact: true,
		name: "transaction",
		component: Transactions,
	},
	{ path: "/report", exact: true, name: "report", component: Report },
	{ path: "/project", exact: true, name: "Project", component: ProjectReport },
	{ path: "/settings", exact: true, name: "settings", component: Organization },
]

const AppRoutes = () => {
	return (
		<Routes>
			{routes.map((route, idx) => {
				return route.component && <Route key={idx} path={route.path} element={<route.component />} />
			})}
		</Routes>
	)
}

export default React.memo(AppRoutes)
