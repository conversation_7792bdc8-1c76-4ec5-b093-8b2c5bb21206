{"name": "app", "version": "0.2.0", "private": true, "type": "module", "dependencies": {"@apollo/client": "^3.10.4", "@clerk/clerk-react": "^5.2.2", "@feedback-fish/react": "^1.2.2", "@google-cloud/storage": "^7.15.2", "@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.3", "@popperjs/core": "^2.11.8", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-progress": "^1.1.0", "@syncfusion/ej2-base": "^26.1.35", "@syncfusion/ej2-data": "^26.1.35", "@syncfusion/ej2-react-buttons": "^26.1.35", "@syncfusion/ej2-react-calendars": "^26.1.35", "@syncfusion/ej2-react-dropdowns": "^26.1.35", "@syncfusion/ej2-react-grids": "^26.1.35", "@syncfusion/ej2-react-inplace-editor": "^26.1.35", "@syncfusion/ej2-react-inputs": "^26.1.35", "@syncfusion/ej2-react-popups": "^26.1.35", "@syncfusion/ej2-react-progressbar": "^26.1.35", "@syncfusion/ej2-react-richtexteditor": "^26.1.35", "@tanstack/react-query": "^5.40.1", "@tremor/react": "^3.17.4", "axios": "^1.7.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "express": "^4.19.2", "firebase": "^10.12.2", "graphql": "^16.8.1", "graphql-request": "^7.0.1", "graphql-tag": "^2.12.6", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "jquery": "^3.7.1", "lucide-react": "^0.454.0", "mongodb": "^6.7.0", "multer": "^1.4.5-lts.1", "popper.js": "^1.16.1", "react": "^18.3.1", "react-bootstrap": "^2.10.2", "react-dom": "^18.3.1", "react-i18next": "^14.1.2", "react-icons": "^5.2.1", "react-iframe": "^1.8.5", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-select": "^5.8.0", "react-toastify": "^10.0.5", "realm": "^12.9.0", "realm-web": "^2.0.1", "recharts": "^2.13.3", "redux": "^5.0.1", "redux-persist": "^6.0.0", "serverless-http": "^3.2.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "web-vitals": "^4.0.1"}, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint './src/**/*.{js,jsx}'", "lint:fix": "eslint './src/**/*.{js,jsx}' --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tanstack/react-query-devtools": "^5.40.1", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^6.2.1", "vite-plugin-eslint": "^1.8.1"}}