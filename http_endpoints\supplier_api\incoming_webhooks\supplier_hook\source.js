// This function is the webhook's request handler.
exports = function (payload, response) {
    // Data can be extracted from the request as follows:

    // Query params, e.g. '?arg1=hello&arg2=world' => {arg1: "918874224", arg2: "848072222"}
    const { arg1, arg2 } = payload.query;

    // Headers, e.g. {"Content-Type": ["application/json"]}
    //const contentTypes = payload.headers["application/json"];

    // Raw request body (if the client sent one).
    // This is a binary object that can be accessed as a string using .text()
    //const body = payload.body;

    // console.log("arg1, arg2: ", arg1, arg2);
    // console.log("Content-Type:", JSON.stringify(contentTypes));
    // console.log("Request body:", body);

    // You can use 'context' to interact with other Realm features.
    // Accessing a value:
    // var x = context.values.get("value_name");

    // Querying a mongodb service:
    //const doc = context.services.get("mongodb-atlas").db("saft").collection("suppliers").findOne({RegistrationNumber: arg1 }, {Suppliers: {"$elemMatch": {RegistrationNumber: arg2}}});

    // The following is not working
    // const doc = context.services.get("mongodb-atlas").db("saft").collection("suppliers").findOneAndUpdate({RegistrationNumber: arg1 }, {$set: { "suppliers.$[elem].NaceCode": "11.123"}}, { arrayFilters: [ { "elem.SupplierID": {$eq: arg2} } ] });
    // const doc = context.services.get("mongodb-atlas").db("saft").collection("suppliers").findAndModify({query: {RegistrationNumber: arg1 }, update: { $set: { "Suppliers.$[elem].NaceCode": "11.123"}}, arrayFilters: [ { "elem.SupplierID": {$eq: arg2} } ] });
    const doc = context.services
        .get("mongodb-atlas")
        .db(context.environment.values.database)
        .collection("suppliers")
        .updateOne(
            { RegistrationNumber: arg1 },
            { $set: { "suppliers.$[elem].NaceCode": "11.125" } },
            { arrayFilters: [{ "elem.SupplierID": { $eq: arg2 } }] }
        );

    // Calling a function:
    // const result = context.functions.execute("function_name", arg1, arg2);

    // The return value of the function is sent as the response back to the client
    // when the "Respond with Result" setting is set.
    return doc;
};
