{"properties": {"AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "AccountIDInRange": {"bsonType": "bool"}, "Amount": {"bsonType": "number"}, "Auto": {"bsonType": "bool"}, "Description": {"bsonType": "array", "items": {"bsonType": "string"}}, "DescriptionDetails": {"bsonType": "string"}, "Emissions": {"bsonType": "object", "properties": {"Scope1": {"bsonType": "object", "properties": {"Combustion": {"bsonType": "object", "properties": {"consumption": {"bsonType": "string"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "units": {"bsonType": "string"}}}, "Processes": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "units": {"bsonType": "double"}}}}, "Refrigerants": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"factor": {"bsonType": "double"}, "gasType": {"bsonType": "string"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "weight": {"bsonType": "double"}}}}, "Vehicles": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"distance": {"bsonType": "double"}, "fuelEconomy": {"bsonType": "double"}, "fuelEconomyUnit": {"bsonType": "string"}, "fuelType": {"bsonType": "string"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}, "vehicleSize": {"bsonType": "string"}}}}}}, "Scope2": {"bsonType": "object", "properties": {"Electricity": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}, "Heat": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "scope3": {"bsonType": "double"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}}}, "Scope3": {"bsonType": "object", "properties": {"Goods": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}, "Waste": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"consumption": {"bsonType": "double"}, "factor": {"bsonType": "double"}, "kgco2e": {"bsonType": "double"}, "source": {"bsonType": "string"}, "type": {"bsonType": "string"}, "unit": {"bsonType": "string"}}}}}}}}, "Flag": {"bsonType": "int"}, "Goods-type": {"bsonType": "string"}, "ImportID": {"bsonType": "string"}, "IsSplittedTransaction": {"bsonType": "bool"}, "Lines": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AccountDescription": {"bsonType": "string"}, "AccountID": {"bsonType": "string"}, "Analysis": {"bsonType": "array", "items": {"bsonType": "object", "properties": {"AnalysisID": {"bsonType": "string"}, "AnalysisType": {"bsonType": "string"}}}}, "CreditAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "DebitAmount": {"bsonType": "object", "properties": {"Amount": {"bsonType": "string"}, "CurrencyAmount": {"bsonType": "string"}, "CurrencyCode": {"bsonType": "string"}, "ExchangeRate": {"bsonType": "string"}}}, "Description": {"bsonType": "string", "default": ""}, "LineAccountIdInRange": {"bsonType": "bool"}, "NaceCode": {"bsonType": "string"}, "RecordID": {"bsonType": "string"}, "ReferenceNumber": {"bsonType": "string"}, "Status": {"bsonType": "string"}, "SupplierID": {"bsonType": "string"}}}}, "NaceCode": {"bsonType": "string"}, "Notes": {"bsonType": "string"}, "Period": {"bsonType": "string"}, "PeriodYear": {"bsonType": "string"}, "Production-related": {"bsonType": "bool"}, "ReferenceNumber": {"bsonType": "string"}, "RegistrationNumber": {"bsonType": "string"}, "Relation": {"bsonType": "string"}, "Scope": {"bsonType": "number"}, "Scope_1": {"bsonType": "number"}, "Scope_2": {"bsonType": "number"}, "Scope_3": {"bsonType": "number"}, "Scope_3_Category": {"bsonType": "number"}, "Status": {"bsonType": "int"}, "SupplierID": {"bsonType": "string"}, "SupplierName": {"bsonType": "string"}, "SystemID": {"bsonType": "string"}, "TransactionDate": {"bsonType": "string"}, "TransactionID": {"bsonType": "string"}, "Type": {"bsonType": "string"}, "_id": {"bsonType": "objectId"}, "kwh": {"bsonType": "number"}, "liter": {"bsonType": "number"}, "mobileCombustion": {"bsonType": "number"}, "non_renewable_energy": {"bsonType": "number"}, "renewable_energy": {"bsonType": "number"}, "stationaryCombustion": {"bsonType": "number"}, "nuclear": {"bsonType": "number"}, "supplierFactor": {"oneOf": [{"minimum": 0, "type": "number"}, {"additionalProperties": false, "patternProperties": {"^[0-9]{4}$": {"type": "number"}}, "type": "object"}]}}, "title": "transaction"}