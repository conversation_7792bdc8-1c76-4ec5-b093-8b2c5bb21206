import { DropDownListComponent } from "@syncfusion/ej2-react-dropdowns"
import { DialogComponent } from "@syncfusion/ej2-react-popups"
import React, { useState } from "react"

export default function TransactionsEditAccountIdDialog({
	showEditAccountIDDialog,
	setShowEditAccountIDDialog,
	updateAccountID,
	t,
}) {
	const accountIdList = [
		{ accountDescription: "Andre nedstrøms kategorier", text: "16 Andre nedstrøms kategorier?!", value: 16 },
		{ accountDescription: "Emballasje", text: "4600 (Emballasje)", value: 4600 },
		{ accountDescription: "Frakt, transportkostnad", text: "6108 (Frakt, transportkostnad)", value: 6108 },
		{ accountDescription: "Forsikring ved frakt", text: "6109 (Forsikring ved frakt)", value: 6109 },
		{ accountDescription: "Elektrisitet i produksjon", text: "6200 (Elektrisitet i produksjon)(2)", value: 6200 },
		{ accountDescription: "Gass i produksjon", text: "6210 (Gass i produksjon)", value: 6210 },
		{ accountDescription: "Fyringsolje i produksjon", text: "6220 (Fyringsolje i produksjon)", value: 6220 },
		{ accountDescription: "Kull, koks i produksjon", text: "6230 (Kull, koks i produksjon)", value: 6230 },
		{ accountDescription: "Ved i produksjon", text: "6240 (Ved i produksjon)(3)", value: 6240 },
		{ accountDescription: "Anleggsdiesel - Gas oil", text: "6257 (Anleggsdiesel)", value: 6257 },
		{ accountDescription: "Bensin  i produksjon", text: "6258 (Bensin  i produksjon)", value: 6258 },
		{ accountDescription: "Diesel i produksjon", text: "6259 (Diesel i produksjon)", value: 6259 },
		{ accountDescription: "Vann i produksjon", text: "6260 (Vann i produksjon)", value: 6260 },
		{ accountDescription: "Annen brensel i produksjon", text: "6290 (Annen brensel i produksjon)", value: 6290 },
		{ accountDescription: "Renovasjon/Avfallshåndtering", text: "6321 (Renovasjon/Avfallshåndtering)", value: 6321 },
		{ accountDescription: "Vann", text: "6322 (Vann)", value: 6322 },
		{ accountDescription: "Lys/elektrisitet kontorer", text: "6341 (Lys/elektrisitet kontorer)(2)", value: 6341 },
		{ accountDescription: "Fjernvarme", text: "6345 (Fjernvarme )(2)", value: 6345 },
		{ accountDescription: "Motordrevet verktøy", text: "6500 (Motordrevet verktøy)", value: 6500 },
		{ accountDescription: "Håndverktøy", text: "6510-30 (Verktøy)(3)", value: 6510 },
		{ accountDescription: "Inventar", text: "6540 (Inventar)(3)", value: 6540 },
		{ accountDescription: "Driftsmateriale", text: "6550 (Driftsmateriale)(3)", value: 6550 },
		{ accountDescription: "Hardware kjøp", text: "6551 (Hardware kjøp)(3)", value: 6551 },
		{
			accountDescription: "Software Måned-/årlig vedlikehold",
			text: "6553 (Software Måned-/årlig vedlikehold)(3)",
			value: 6553,
		},
		{ accountDescription: "Arbeidsklær og verneutstyr", text: "6570 (Arbeidsklær og verneutstyr)(3)", value: 6570 },
		{ accountDescription: "Annet driftsmateriale", text: "6590 (Annet driftsmateriale)(3)", value: 6590 },
		{ accountDescription: "Bensin til transportmidler", text: "7016 (Bensin til transportmidler)(1)", value: 7016 },
		{ accountDescription: "Diesel til transportmidler", text: "7017 (Diesel til transportmidler)(1)", value: 7017 },
		{
			accountDescription: "Elektrisitet til transportmidler",
			text: "7018 (Elektrisitet til transportmidler)(2)",
			value: 7018,
		},
		{ accountDescription: "Vedlikehold transportmidler", text: "7020 (Vedlikehold transportmidler)(3)", value: 7020 },
		{ accountDescription: "Forsikring transportmidler", text: "7040 (Forsikring transportmidler)(3)", value: 7040 },
		{
			accountDescription: "Annen kostnad transportmidler",
			text: "7090 (Annen kostnad transportmidler)(3)",
			value: 7090,
		},
	]

	const buttons = [
		{
			buttonModel: {
				content: "Save",
				cssClass: "e-flat",
				isPrimary: true,
			},
			click: () => {
				setShowEditAccountIDDialog(false)
				updateAccountID(accountIDEditvalue, accountIDEditDescription)
				setAccountIDEditValue(4600)
			},
		},
		{
			buttonModel: {
				content: "Cancel",
				cssClass: "e-flat",
			},
			click: () => {
				setShowEditAccountIDDialog(false)
			},
		},
	]

	const dropDownFields = { text: "text", value: "value" }

	//const [accountID, setAccountID] = useState(accountIdList[0].value)

	const [accountIDEditvalue, setAccountIDEditValue] = useState(accountIdList[0].value)

	const [accountIDEditDescription, setAccountIDEditDescription] = useState("")

	const onChangeAccountID = (args) => {
		//setAccountID(args.itemData.value)

		setAccountIDEditValue(args.itemData.value)
		setAccountIDEditDescription(args.itemData.accountDescription)
	}

	return (
		<div
			id='dialog-target-account'
			style={{ position: "absolute", top: "10%", left: "40%", height: "250px", width: "450px" }}
		>
			<DialogComponent
				target='#dialog-target-account'
				close={() => setShowEditAccountIDDialog(false)}
				header='Edit Account Id'
				visible={showEditAccountIDDialog}
				showCloseIcon={true}
				allowDragging={true}
				buttons={buttons}
			>
				<div style={{}}>
					<div className='form-group col-md-12'>
						<div className='e-float-input e-control-wrapper'>
							<DropDownListComponent
								id='AccountID'
								name='AccountID'
								value={accountIDEditvalue}
								//itemTemplate={this.accountIDItemTemplate}
								//valueTemplate={this.accountIDValueTemplate}
								dataSource={accountIdList}
								fields={dropDownFields}
								change={onChangeAccountID}
								setAccountIDEditValue={setAccountIDEditValue}
								setAccountIDEditDescription={setAccountIDEditDescription}
							/>
							<input style={{ display: "none" }} />
							<span className='e-float-line'></span>
							<label className='e-float-text e-label-top'>{t("AccountID")}</label>
						</div>
					</div>
				</div>
			</DialogComponent>
		</div>
	)
}
