// Function to get a signed URL for direct upload
const getSignedUrl = async (file, foldername) => {
	const timestamp = Date.now()
	const response = await fetch("/.netlify/functions/generate-signed-url", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({
			fileName: `${foldername}/${timestamp}_${file.name}`,
			contentType: file.type,
			fileSize: file.size,
		}),
	})

	if (!response.ok) {
		throw new Error(`Failed to get signed URL: ${response.status}`)
	}

	return response.json()
}

// Function to complete the upload process
const completeUpload = async (fileData) => {
	const response = await fetch("/.netlify/functions/complete-upload", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(fileData),
	})

	if (!response.ok) {
		throw new Error(`Failed to complete upload: ${response.status}`)
	}

	return response.json()
}

// Function to upload file through direct upload with signed URL
export const uploadFileToGCS = async (file, foldername, onProgress) => {
	try {
		// Get a signed URL for direct upload
		const { signedUrl, fileId, fileName, contentType } = await getSignedUrl(file, foldername)
		// Upload file directly to GCS using the signed URL
		return new Promise((resolve, reject) => {
			const xhr = new XMLHttpRequest()

			// Set up progress tracking
			xhr.upload.addEventListener("progress", (event) => {
				if (event.lengthComputable && onProgress) {
					const progressPercentage = Math.round((event.loaded / event.total) * 100)
					onProgress(progressPercentage)
				}
			})

			// Handle successful completion
			xhr.onload = async () => {
				if (xhr.status >= 200 && xhr.status < 300) {
					try {
						// Notify backend that upload is complete
						const fileInfo = await completeUpload({
							fileId,
							fileName,
							contentType,
							fileSize: file.size,
						})

						resolve(fileInfo)
					} catch (error) {
						reject(error)
					}
				} else {
					reject(new Error(`Upload failed with status: ${xhr.status}`))
				}
			}

			// Handle errors
			xhr.onerror = () => {
				reject(new Error("Network error occurred during upload"))
			}

			// Open connection and send the request
			xhr.open("PUT", signedUrl, true)
			xhr.setRequestHeader("Content-Type", contentType)
			xhr.send(file)
		})
	} catch (error) {
		console.error("Upload failed:", error)
		throw error
	}
}

// Function to fetch file history from our backend proxy
export const getFileHistory = async () => {
	try {
		const response = await fetch("/.netlify/functions/get-files")

		if (!response.ok) {
			throw new Error(`Failed to fetch file history: ${response.status}`)
		}

		const files = await response.json()

		// Sort by uploadedAt (newest first)
		return files.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime())
	} catch (error) {
		console.error("Failed to fetch file history:", error)
		return []
	}
}

// Function to delete a file through our backend proxy
export const deleteFile = async (fileId) => {
	try {
		const response = await fetch(`/.netlify/functions/delete-file`, {
			method: "DELETE",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ fileId }),
		})

		if (!response.ok) {
			throw new Error(`Failed to delete file: ${response.status}`)
		}
	} catch (error) {
		console.error("Failed to delete file:", error)
		throw error
	}
}
