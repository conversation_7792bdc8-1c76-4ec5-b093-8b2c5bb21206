import { Card, Text, Badge, <PERSON><PERSON><PERSON>, Flex } from "@tremor/react"
import React from "react"

import Spinner from "../../components/ui/Spinner"
import { valueFormat, getPrimaryDataColors } from "../../services/helper"

export default function Scope3Category({
	chartData = [],
	categoryData = [],
	primaryData = [],
	isScopeCategoryData = false,
	language = "en-EN",
	t = () => {},
	handleScopeCatagoryFiltering = () => {},
}) {
	const getNameValues = (data) => {
		return Object.keys(data)
	}

	const nameValues = getNameValues(chartData)

	const colorValues = ["red", "amber", "blue", "lime", "cyan", "emerald", "pink", "purple"].reverse()

	const badgeColorValues = ["red", "amber", "blue", "lime", "cyan", "emerald", "pink", "purple"]

	const getCatagoryId = (value) => {
		return categoryData.findIndex((catagory) => catagory.Description === value)
	}

	const customTooltip = ({ payload, active }) => {
		if (!active || !payload) return null
		return (
			<div className="w-96 2xl:w-[30rem] rounded-tremor-default text-tremor-default bg-tremor-background p-2 shadow-tremor-dropdown border border-tremor-border absolute top-96 lg:left-6 right-0">
				{payload
					.slice()
					.reverse()
					.map((category, idx) => {
						return (
							<div key={idx} className="grid w-full grid-cols-[24px_1fr_64px] items-center gap-2">
								<div className={`m-1 h-2 w-2 justify-self-center rounded ${"bg-" + category.color + "-500"} rounded`} />
								<p className="text-tremor-content truncate">{`${t("Category")} ${
									getCatagoryId(category.dataKey) + 1
								} - ${t(category.dataKey)}`}</p>
								<p className="font-medium text-tremor-content-emphasis justify-self-end">
									{valueFormat(category.value, language)}
								</p>
							</div>
						)
					})}
			</div>
		)
	}

	return (
		<div className="flex flex-col gap-3">
			<div className="ml-2 text-xl font-bold">{t("Scope 3 categories")}</div>
			{isScopeCategoryData ? (
				<div className="flex w-full gap-2 self-start">
					<Card className="w-12 h-full p-1 z-40 flex flex-col justify-center">
						<BarChart
							className="h-full"
							data={[chartData]}
							index="label"
							showLegend={false}
							showYAxis={false}
							showXAxis={false}
							noDataText
							categories={nameValues}
							colors={colorValues}
							stack={true}
							showAnimation
							customTooltip={customTooltip}
							yAxisWidth={4}
						/>
					</Card>
					<Flex flexDirection="col" className="gap-2 z-10">
						{categoryData.map((item, index) => (
							<Card
								key={index}
								className="gap-3 cursor-pointer"
								onClick={() => handleScopeCatagoryFiltering(index + 1)}
							>
								<Flex alignItems="start">
									<Text className="font-bold">{`${t("Category")} ${index + 1} - ${t(item.Description)}`}</Text>
									<Badge color={badgeColorValues[index]}>{valueFormat(item.Percentage, language)}</Badge>
								</Flex>
								<Flex justifyContent="start" alignItems="baseline" className="space-x-2 truncate">
									<div className="text-2xl font-semibold">
										{Intl.NumberFormat(language, {
											maximumFractionDigits: 2,
										}).format(item.Emission)}
									</div>
									<span className="truncate">
										<Text>tco2e</Text>
									</span>
								</Flex>
								<Flex justifyContent="start" alignItems="baseline" className="space-x-2 truncate">
									<div
										className={`text-md font-bold mx-0 text-${getPrimaryDataColors(
											primaryData[index]?.Percentage || 0
										)}-500`}
									>
										{Intl.NumberFormat(language, {
											maximumFractionDigits: 2,
										}).format(primaryData[index]?.Percentage || 0)}{" "}
										%
									</div>
									<span className="truncate">
										<Text>{t("common:primary")}</Text>
									</span>
								</Flex>
							</Card>
						))}
					</Flex>
				</div>
			) : (
				<Spinner type="md" />
			)}
		</div>
	)
}
