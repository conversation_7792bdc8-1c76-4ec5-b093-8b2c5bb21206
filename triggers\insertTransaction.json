{"name": "insertTransaction", "type": "DATABASE", "disabled": false, "config": {"collection": "transaction", "database": "test-scope321", "full_document": true, "full_document_before_change": false, "match": {}, "maximum_throughput": false, "operation_types": ["INSERT"], "project": {}, "service_name": "mongodb-atlas", "skip_catchup_events": false, "tolerate_resume_errors": false, "unordered": true}, "event_processors": {"FUNCTION": {"config": {"function_name": "insertTransactionFunction"}}}}