.main-container {
    position: relative;
    padding: 0px 10px;
}

.container {
    padding-bottom: 10px;
    /* border: dashed grey 1px; */
    background-color: #f8fafc;
    border-radius: 10px;
}

.buttonContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
}

dl,
ol,
ul {
    padding-left: 2rem;
    list-style: disc;
}

.e-rte-table.e-dashed-border td,
.e-rte-table.e-dashed-border th {
    border: none;
    height: 20px;
    min-width: 20px;
    padding: 2px 5px;
    vertical-align: middle;
}