[{"_id": {"$oid": "67abcdbf273f786abebfa917"}, "ID": "23_318_3188_11_1", "Scope": "Scope 3", "Level 1": "Business travel- sea", "Level 2": "Ferry", "Level 3": "Foot passenger", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.01871}, {"_id": {"$oid": "67abcdbf273f786abebfa918"}, "ID": "23_318_3189_11_1", "Scope": "Scope 3", "Level 1": "Business travel- sea", "Level 2": "Ferry", "Level 3": "Car passenger", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.12933}, {"_id": {"$oid": "67abcdbf273f786abebfa919"}, "ID": "23_318_3190_11_1", "Scope": "Scope 3", "Level 1": "Business travel- sea", "Level 2": "Ferry", "Level 3": "Average (all passenger)", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.1127}, {"_id": {"$oid": "67abcdbf273f786abebfa91d"}, "ID": "25_301_3045_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.13994}, {"_id": {"$oid": "67abcdbf273f786abebfa91e"}, "ID": "25_301_3046_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.1437}, {"_id": {"$oid": "67abcdbf273f786abebfa91f"}, "ID": "25_301_3047_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.11274}, {"_id": {"$oid": "67abcdbf273f786abebfa920"}, "ID": "25_301_3050_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.14262}, {"_id": {"$oid": "67abcdbf273f786abebfa921"}, "ID": "25_301_3051_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.06078}, {"_id": {"$oid": "67abcdbf273f786abebfa922"}, "ID": "25_301_3052_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Small car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04284}, {"_id": {"$oid": "67abcdbf273f786abebfa923"}, "ID": "25_301_3053_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.16807}, {"_id": {"$oid": "67abcdbf273f786abebfa924"}, "ID": "25_301_3054_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.17726}, {"_id": {"$oid": "67abcdbf273f786abebfa925"}, "ID": "25_301_3055_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.1149}, {"_id": {"$oid": "67abcdbf273f786abebfa926"}, "ID": "25_301_3058_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.17256}, {"_id": {"$oid": "67abcdbf273f786abebfa927"}, "ID": "25_301_3059_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.09312}, {"_id": {"$oid": "67abcdbf273f786abebfa928"}, "ID": "25_301_3060_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Medium car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04625}, {"_id": {"$oid": "67abcdbf273f786abebfa929"}, "ID": "25_301_3061_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.20729}, {"_id": {"$oid": "67abcdbf273f786abebfa92a"}, "ID": "25_301_3062_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.26885}, {"_id": {"$oid": "67abcdbf273f786abebfa92b"}, "ID": "25_301_3063_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.15486}, {"_id": {"$oid": "67abcdbf273f786abebfa92c"}, "ID": "25_301_3066_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.22472}, {"_id": {"$oid": "67abcdbf273f786abebfa92d"}, "ID": "25_301_3067_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.11923}, {"_id": {"$oid": "67abcdbf273f786abebfa92e"}, "ID": "25_301_3068_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Large car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04925}, {"_id": {"$oid": "67abcdbf273f786abebfa92f"}, "ID": "25_301_3069_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Diesel", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.16984}, {"_id": {"$oid": "67abcdbf273f786abebfa930"}, "ID": "25_301_3070_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Petrol", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.1645}, {"_id": {"$oid": "67abcdbf273f786abebfa931"}, "ID": "25_301_3071_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Hybrid", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.12607}, {"_id": {"$oid": "67abcdbf273f786abebfa932"}, "ID": "25_301_3074_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Unknown", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.16691}, {"_id": {"$oid": "67abcdbf273f786abebfa933"}, "ID": "25_301_3075_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Plug-in Hybrid Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.10853}, {"_id": {"$oid": "67abcdbf273f786abebfa934"}, "ID": "25_301_3076_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Cars (by size)", "Level 3": "Average car", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "Battery Electric Vehicle", "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04745}, {"_id": {"$oid": "67abcdbf273f786abebfa935"}, "ID": "25_302_3077_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Motorbike", "Level 3": "Small", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.08319}, {"_id": {"$oid": "67abcdbf273f786abebfa936"}, "ID": "25_302_3078_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Motorbike", "Level 3": "Medium", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.10107}, {"_id": {"$oid": "67abcdbf273f786abebfa937"}, "ID": "25_302_3079_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Motorbike", "Level 3": "Large", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.13252}, {"_id": {"$oid": "67abcdbf273f786abebfa938"}, "ID": "25_302_3080_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Motorbike", "Level 3": "Average", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.11367}, {"_id": {"$oid": "67abcdbf273f786abebfa939"}, "ID": "25_313_3141_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Taxis", "Level 3": "Electric taxi", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "", "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.04925}, {"_id": {"$oid": "67abcdbf273f786abebfa93a"}, "ID": "25_313_3141_4_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Taxis", "Level 3": "Regular taxi", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.20805}, {"_id": {"$oid": "67abcdbf273f786abebfa93b"}, "ID": "25_314_3144_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Bus", "Level 3": "Electric bus", "Level 4": {"$numberDouble": "NaN"}, "Column Text": "", "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.01925}, {"_id": {"$oid": "67abcdbf273f786abebfa93c"}, "ID": "25_314_3145_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Bus", "Level 3": "Average local bus", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.10846}, {"_id": {"$oid": "67abcdbf273f786abebfa93d"}, "ID": "25_314_3146_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Bus", "Level 3": "Coach", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.02717}, {"_id": {"$oid": "67abcdbf273f786abebfa93e"}, "ID": "25_315_3147_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Rail", "Level 3": "National rail", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.03546}, {"_id": {"$oid": "67abcdbf273f786abebfa93f"}, "ID": "25_315_3148_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Rail", "Level 3": "International rail", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.00446}, {"_id": {"$oid": "67abcdbf273f786abebfa940"}, "ID": "25_315_3149_11_1", "Scope": "Scope 3", "Level 1": "Business travel- land", "Level 2": "Rail", "Level 3": "Light rail and tram", "Level 4": {"$numberDouble": "NaN"}, "Column Text": {"$numberDouble": "NaN"}, "UOM": "passenger.km", "GHG/Unit": "kg CO2e", "GHG Conversion Factor": 0.0286}]