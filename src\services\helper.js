export const valueFormat = (number, language = "en-EN", type = "percent") => {
	return `${new Intl.NumberFormat(language, {
		maximumFractionDigits: 2,
	})
		.format(number)
		.toString()} ${type === "percent" ? "%" : ""}`
}

export const getPrimaryDataColors = (value) => {
	try {
		let color = ""
		if (value < 50) {
			color = "rose"
		} else if (value >= 50 && value <= 75) {
			color = "amber"
		} else {
			color = "teal"
		}
		return color
	} catch (error) {}
}
