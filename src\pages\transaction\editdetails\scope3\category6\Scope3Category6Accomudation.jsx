import React, { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { RiDeleteBin6Line } from "react-icons/ri"
import Select from "react-select"

import Button from "../../../../../components/ui/Button" // Assuming path is correct relative to the new location
import Input from "../../../../../components/ui/Input" // Assuming path is correct relative to the new location
import { HOTELS_COUNTRY_FACTORS, CATEGORY6A_TYPE_DATA } from "../contants/category6"

// Import constants from the correct location

// Main component following the Category 5 pattern
export default function Scope3Category6Accomudation(props) {
	const {
		accommodationRows = [], // Renamed from transactionData?.category6A
		updateAccommodationEmission, // Function to notify parent of changes for total calculation
	} = props

	const { t } = useTranslation("transactions")
	const numberInputRef = useRef() // Ref for the 'number' input in the add form

	// --- State for the "Add New" form ---
	const [accommodationType, setAccommodationType] = useState(CATEGORY6A_TYPE_DATA[0])
	const [accommodationTypeLabel, setAccommodationTypeLabel] = useState(CATEGORY6A_TYPE_DATA[0].label)
	const [country, setCountry] = useState(HOTELS_COUNTRY_FACTORS[0])
	const [countryLabel, setCountryLabel] = useState(HOTELS_COUNTRY_FACTORS[0].label)
	const [numberOfPeople, setNumberOfPeople] = useState(1) // Number of rooms/units
	const [numberOfNights, setNumberOfNights] = useState(1) // Number of nights
	const numberUnit = "people" // Assuming constant unit
	const nightUnit = "nights" // Assuming constant unit

	// --- Input Handlers for "Add New" form ---
	const handleTypeChange = (selectedOption) => {
		setAccommodationType(selectedOption)
		setAccommodationTypeLabel(selectedOption.label)
		// Optional: Focus next input
		// numberInputRef.current?.focus();
	}

	const handleCountryChange = (selectedOption) => {
		setCountryLabel(selectedOption.label)
		setCountry(selectedOption)
		// Optional: Focus next input
		// numberInputRef.current?.focus();
	}

	const handleNumberChange = (event) => {
		setNumberOfPeople(event.target.value)
	}

	const handleNightChange = (event) => {
		setNumberOfNights(event.target.value)
	}

	const handleFocus = (event) => event.target.select()

	// --- Add/Delete Logic ---

	// Adds the validated and calculated new row to the state
	const addAccommodation = (calculatedScope3) => {
		const newRow = {
			accommodationType: accommodationTypeLabel,
			country: countryLabel,
			numberOfPeople: Number(numberOfPeople),
			numberOfNights: Number(numberOfNights),
			scope3: Number(calculatedScope3),
		}

		const updatedRows = [...accommodationRows, newRow]
		updateAccommodationEmission(newRow, false, "scope3Category6AccommodationRows", updatedRows, 6)
		// Reset form
		setAccommodationType(CATEGORY6A_TYPE_DATA[0])
		setCountry(HOTELS_COUNTRY_FACTORS[0])
		setNumberOfPeople(1)
		setNumberOfNights(1)
	}

	// Validates form, calculates emission, and calls addAccommodation
	const handleAddEmission = () => {
		const factor = country.factor
		const calculatedScope3 = numberOfPeople * numberOfNights * factor
		addAccommodation(calculatedScope3)
	}

	// Deletes a row from the list by index
	const deleteAccommodationRow = (index) => {
		let newRows = [...accommodationRows]

		newRows.splice(index, 1)

		updateAccommodationEmission(accommodationRows[index], true, "scope3Category6AccommodationRows", newRows, 6)
	}

	return (
		<div className='scope-category-container'>
			{accommodationRows.map((row, index) => (
				<div key={row.id || index}>
					{" "}
					{/* Use a stable key if available */}
					<div className='grid grid-cols-[2fr_1fr_1fr_1fr_1fr_1fr] gap-4 my-1 saved-emission items-center'>
						{" "}
						{/* Adjusted columns */}
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("AccommodationType")}</label>
							<div className='text-sm'>{row.accommodationType || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Country")}</label>
							<div className='text-sm'>{row.country || "N/A"}</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Number")}</label>
							<div className='flex relative text-sm'>
								<span>{row.numberOfPeople}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'> people</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("Nights")}</label>
							<div className='flex relative text-sm'>
								<span>{row.numberOfNights}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'>nights</span>
							</div>
						</div>
						<div>
							<label className='text-slate-800 font-semibold whitespace-nowrap'>{t("TotalCo2e")}</label>
							<div className='flex relative text-sm'>
								<span>{Number(row.scope3 || 0).toFixed(2)}</span>
								<span className='text-xs text-gray-500 ml-1 mt-0.5'>{t("kg")}</span>
							</div>
						</div>
						<div className='flex justify-center items-center'>
							<button
								type='button'
								onClick={() => deleteAccommodationRow(index)}
								className='text-red-500 hover:text-red-700 p-1'
								aria-label={t("DeleteAccommodationRow")}
							>
								<RiDeleteBin6Line size={24} />
							</button>
						</div>
					</div>
					<hr className='my-2' />
				</div>
			))}

			{/* Form for adding new rows */}
			<div className='grid grid-cols-5 gap-4 my-3 pt-3 border-t border-gray-200'>
				{" "}
				{/* Adjusted columns */}
				<div>
					<label className='text-slate-800 text-sm font-semibold whitespace-nowrap' htmlFor='c6a-new-type'>
						{t("AccommodationType")}
					</label>
					<Select
						id='c6a-new-type'
						value={accommodationType}
						onChange={handleTypeChange}
						options={CATEGORY6A_TYPE_DATA}
						placeholder={t("Select...")}
						classNamePrefix='react-select'
					/>
				</div>
				<div>
					<label className='text-slate-800 text-sm font-semibold whitespace-nowrap' htmlFor='c6a-new-country'>
						{t("Country")}
					</label>
					<Select
						id='c6a-new-country'
						value={country}
						onChange={handleCountryChange}
						options={HOTELS_COUNTRY_FACTORS}
						placeholder={t("Select...")}
						classNamePrefix='react-select'
					/>
				</div>
				<div>
					<Input
						label={t("# People")}
						id='c6a-new-number'
						ref={numberInputRef}
						handleFocus={handleFocus}
						unit={numberUnit}
						placeholder='0'
						type='number'
						min='0'
						step='any'
						value={numberOfPeople}
						handleChange={handleNumberChange}
						labelClassName='text-sm font-semibold'
					/>
				</div>
				<div>
					<Input
						label={t("# Nights")}
						id='c6a-new-night'
						unit={nightUnit}
						placeholder='0'
						type='number'
						min='0'
						step='any'
						value={numberOfNights}
						handleChange={handleNightChange}
						handleFocus={handleFocus}
						labelClassName='text-sm font-semibold'
					/>
				</div>
				<div className='self-end mb-1'>
					<Button title={`${t("Add")} | +`} handleClick={handleAddEmission} color='sky-500' size='sm' />
				</div>
			</div>
		</div>
	)
}
